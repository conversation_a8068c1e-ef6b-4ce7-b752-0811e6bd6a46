﻿// Designed by KINEMATION, 2025.

using KINEMATION.KAnimationCore.Runtime.Attributes;
using KINEMATION.KAnimationCore.Runtime.Rig;

using System.Collections.Generic;
using UnityEngine;

namespace KINEMATION.MagicBlend.Runtime
{
    public class MagicBlendAsset : ScriptableObject, IRigUser
    {
        [Header("Rig")]
        public KRig rigAsset;
        
        [Header("Blending")]
        [Min(0f)] public float blendTime = 0.15f;
        public AnimationCurve blendCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        
        [Header("Poses")]
        public AnimationClip basePose;
        public AnimationClip overlayPose;
        public List<OverrideOverlay> overrideOverlays = new List<OverrideOverlay>();
        [Min(0f)] public float overlaySpeed = 1f;
        [Tooltip("If Overlay is static or not.")]
        public bool isAnimation = false;
        
        [Unfold] public List<LayeredBlend> layeredBlends = new List<LayeredBlend>();
        [Range(0f, 1f)] public float globalWeight = 1f;

        public KRig GetRigAsset()
        {
            return rigAsset;
        }

        public bool HasOverrides()
        {
            if (overrideOverlays.Count == 0) return false;
            foreach (var overlay in overrideOverlays) if (overlay.overlay == null) return false;
            return true;
        }

        #region Runtime Modification Functions

        /// <summary>
        /// Sets the base pose at runtime. Useful for dynamic animation switching.
        /// </summary>
        /// <param name="newBasePose">The new base pose animation clip</param>
        public void SetBasePose(AnimationClip newBasePose)
        {
            basePose = newBasePose;
        }

        /// <summary>
        /// Sets the overlay pose at runtime. Useful for dynamic animation switching.
        /// </summary>
        /// <param name="newOverlayPose">The new overlay pose animation clip</param>
        public void SetOverlayPose(AnimationClip newOverlayPose)
        {
            overlayPose = newOverlayPose;
        }

        /// <summary>
        /// Sets the overlay speed at runtime.
        /// </summary>
        /// <param name="newSpeed">The new overlay speed multiplier</param>
        public void SetOverlaySpeed(float newSpeed)
        {
            overlaySpeed = Mathf.Max(0f, newSpeed);
        }

        /// <summary>
        /// Sets the global weight at runtime.
        /// </summary>
        /// <param name="newWeight">The new global weight (0-1)</param>
        public void SetGlobalWeight(float newWeight)
        {
            globalWeight = Mathf.Clamp01(newWeight);
        }

        /// <summary>
        /// Sets the blend time at runtime.
        /// </summary>
        /// <param name="newBlendTime">The new blend time</param>
        public void SetBlendTime(float newBlendTime)
        {
            blendTime = Mathf.Max(0f, newBlendTime);
        }

        /// <summary>
        /// Sets whether the overlay is treated as an animation or static pose.
        /// </summary>
        /// <param name="isAnimated">True if overlay should be treated as animation</param>
        public void SetIsAnimation(bool isAnimated)
        {
            isAnimation = isAnimated;
        }

        /// <summary>
        /// Adds or updates a layered blend at runtime.
        /// </summary>
        /// <param name="layerName">Name of the layer to modify</param>
        /// <param name="baseWeight">Base weight for the blend</param>
        /// <param name="additiveWeight">Additive weight for the blend</param>
        /// <param name="localWeight">Local weight for the blend</param>
        public void SetLayeredBlend(string layerName, float baseWeight, float additiveWeight = 0f, float localWeight = 0f)
        {
            if (string.IsNullOrEmpty(layerName)) return;

            // Find existing layered blend
            var existingBlend = layeredBlends.Find(lb => lb.layer != null && lb.layer.name == layerName);
            if (existingBlend != null)
            {
                existingBlend.baseWeight = baseWeight;
                existingBlend.additiveWeight = additiveWeight;
                existingBlend.localWeight = localWeight;
            }
            else
            {
                Debug.LogWarning($"Layered blend with layer name '{layerName}' not found in {name}");
            }
        }

        /// <summary>
        /// Adds or updates an override overlay at runtime.
        /// </summary>
        /// <param name="index">Index of the override to modify</param>
        /// <param name="newOverlay">New overlay animation clip</param>
        /// <param name="weight">Weight of the overlay</param>
        /// <param name="mask">Optional avatar mask</param>
        public void SetOverrideOverlay(int index, AnimationClip newOverlay, float weight = 1f, AvatarMask mask = null)
        {
            if (index < 0) return;

            // Ensure the list is large enough
            while (overrideOverlays.Count <= index)
            {
                overrideOverlays.Add(new OverrideOverlay());
            }

            overrideOverlays[index].overlay = newOverlay;
            overrideOverlays[index].weight = weight;
            overrideOverlays[index].mask = mask;
        }

        /// <summary>
        /// Removes an override overlay at runtime.
        /// </summary>
        /// <param name="index">Index of the override to remove</param>
        public void RemoveOverrideOverlay(int index)
        {
            if (index >= 0 && index < overrideOverlays.Count)
            {
                overrideOverlays.RemoveAt(index);
            }
        }

        /// <summary>
        /// Clears all override overlays.
        /// </summary>
        public void ClearOverrideOverlays()
        {
            overrideOverlays.Clear();
        }

        #endregion
    }
}