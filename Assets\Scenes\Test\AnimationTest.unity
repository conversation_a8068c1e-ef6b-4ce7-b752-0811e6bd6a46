%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &5386602
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5386603}
  - component: {fileID: 5386604}
  m_Layer: 0
  m_Name: CC_Base_Tongue
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5386603
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5386602}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5386604
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5386602}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e053034ff17f24c4786eea07a8c73342, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1654403436102926152, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1347422854}
  - {fileID: 1699357223}
  - {fileID: 1926856646}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 1926856646}
  m_AABB:
    m_Center: {x: -0.03286753, y: 0.003165951, z: -0.00006536394}
    m_Extent: {x: 0.054333396, y: 0.029733827, z: 0.025414914}
  m_DirtyAABB: 0
--- !u!1 &5686443
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5686444}
  m_Layer: 0
  m_Name: LeftHandThumb4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5686444
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5686443}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.025043216, y: 0.0000001335144, z: -2.8421688e-16}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 478356027}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &14398074
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1911527513}
    m_Modifications:
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_RootOrder
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.9897865
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 1.0257374
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.9850558
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.052045982
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.013365984
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.0012076038
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.07352169
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.05951578
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.804304
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.5866409
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 69.216
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 147.989
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -30.857
      objectReference: {fileID: 0}
    - target: {fileID: 6360584711328263943, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_Name
      value: SciFi_Pistol_4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 2032455264}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 659361831}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 200421604}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 278449097}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c900257853a7a74f9b11e31d554c873, type: 3}
--- !u!1 &46562586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 46562587}
  m_Layer: 6
  m_Name: ForwardAimTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &46562587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 46562586}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.049999952, y: 1.09, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &47061096
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 47061097}
  - component: {fileID: 47061098}
  m_Layer: 0
  m_Name: DummyMESH
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &47061097
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 47061096}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1023502522}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &47061098
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 47061096}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
  m_Bones:
  - {fileID: 519551633}
  - {fileID: 735305304}
  - {fileID: 1569415569}
  - {fileID: 513596237}
  - {fileID: 2041163931}
  - {fileID: 943684963}
  - {fileID: 651974929}
  - {fileID: 1238991691}
  - {fileID: 1234249420}
  - {fileID: 1299817719}
  - {fileID: 163466185}
  - {fileID: 1229944065}
  - {fileID: 764028470}
  - {fileID: 1005313729}
  - {fileID: 889734683}
  - {fileID: 1891256920}
  - {fileID: 1863733099}
  - {fileID: 2115578593}
  - {fileID: 1492066494}
  - {fileID: 1114303337}
  - {fileID: 1296802776}
  - {fileID: 478356027}
  - {fileID: 1891208916}
  - {fileID: 236590512}
  - {fileID: 1010048933}
  - {fileID: 1204222773}
  - {fileID: 823157133}
  - {fileID: 1911527513}
  - {fileID: 2048931244}
  - {fileID: 1525825665}
  - {fileID: 234521622}
  - {fileID: 849249593}
  - {fileID: 66172616}
  - {fileID: 958973885}
  - {fileID: 1012358140}
  - {fileID: 2082184160}
  - {fileID: 335438796}
  - {fileID: 1479432771}
  - {fileID: 872981094}
  - {fileID: 692375174}
  - {fileID: 614345930}
  - {fileID: 2087275942}
  - {fileID: 1872868590}
  - {fileID: 435474585}
  - {fileID: 1817938615}
  - {fileID: 1659492429}
  - {fileID: 1445606876}
  - {fileID: 1291548215}
  - {fileID: 311320240}
  - {fileID: 1537216688}
  - {fileID: 525762877}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 519551633}
  m_AABB:
    m_Center: {x: 0.00070747733, y: -0.09512186, z: 0.037387706}
    m_Extent: {x: 0.5886419, y: 0.9141923, z: 0.20262784}
  m_DirtyAABB: 0
--- !u!1 &66172615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 66172616}
  m_Layer: 0
  m_Name: RightHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &66172616
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 66172615}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000014876733, y: 0.000000027567928, z: 0.19689088, w: 0.9804254}
  m_LocalPosition: {x: 0.033757787, y: 0.0000017077673, z: 0.0000028001668}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2082184160}
  m_Father: {fileID: 1525825665}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &67450124
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 67450125}
  - component: {fileID: 67450126}
  m_Layer: 6
  m_Name: AimIK After Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &67450125
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 67450124}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 196230464}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &67450126
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 67450124}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: -5.905176, y: -0.586779, z: 3.4299836}
    IKPositionWeight: 1
    root: {fileID: 67450125}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 6
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 735305304}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.000000019484096, y: 0.1172419, z: 1.3518631e-15}
      defaultLocalRotation: {x: -0.028183335, y: 0.000000047389825, z: -0.000000039998238,
        w: 0.9996028}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 1569415569}
      weight: 0.721
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -0.0000000055014895, y: 0.17185535, z: 0.0039671953}
      defaultLocalRotation: {x: -0.057572376, y: -0.00000003575419, z: -0.0000000013925856,
        w: 0.9983414}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 1891208916}
      weight: 0.433
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.00000011290042, y: 0.24456844, z: 0.0013742375}
      defaultLocalRotation: {x: 0.21772775, y: -0.000000054086108, z: 0.000000021804336,
        w: 0.97600955}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 2032455264}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -8.146297, y: 2.7253227, z: 4.846797}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &95360379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 95360380}
  m_Layer: 0
  m_Name: ball_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &95360380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 95360379}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4711623, y: -0.020159509, z: 0.024802616, w: 0.8814673}
  m_LocalPosition: {x: -0.0000018896537, y: 0.14396675, z: 0.00000074505806}
  m_LocalScale: {x: 1, y: 0.9999998, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1065482879}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &122185266
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 122185267}
  - component: {fileID: 122185268}
  m_Layer: 6
  m_Name: AimIK After Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &122185267
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122185266}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &122185268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122185266}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 1.234939, y: -1.4151461, z: 0.32664528}
    IKPositionWeight: 1
    root: {fileID: 122185267}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 6
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 791390003}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 3.352761e-10, y: 0.038068235, z: 0.00000034332274}
      defaultLocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738,
        w: 0.99217784}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 2036937282}
      weight: 0.721
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -7.8231094e-10, y: 0.12466316, z: 0.0000017356872}
      defaultLocalRotation: {x: -0.15933207, y: -0.00021606042, z: -0.0018017701,
        w: 0.9872234}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 2084455373}
      weight: 0.433
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.0000000077486035, y: 0.26940826, z: -0.0000020599364}
      defaultLocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 788945576}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -0.8694594, y: 1.5595894, z: 2.499767}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &123809141
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 123809142}
  m_Layer: 0
  m_Name: ring_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &123809142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 123809141}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0017240138, y: 0.019061547, z: 0.3346126, w: -0.9421614}
  m_LocalPosition: {x: -0.0000015766129, y: 0.027640875, z: -0.0000021375859}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1331996526}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &129380940
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 129380941}
  m_Layer: 0
  m_Name: thumb_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &129380941
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 129380940}
  serializedVersion: 2
  m_LocalRotation: {x: 0.21791503, y: 0.01546745, z: -0.0679087, w: -0.9734795}
  m_LocalPosition: {x: -0.0000007273631, y: 0.05436759, z: -0.00000074878346}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1253068708}
  m_Father: {fileID: 439328758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &130749903
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 130749904}
  m_Layer: 0
  m_Name: index_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &130749904
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 130749903}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0128645245, y: 0.01292789, z: 0.3180652, w: 0.94789344}
  m_LocalPosition: {x: -0.0004373443, y: 0.028221816, z: -0.0007229042}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 479989158}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &163466184
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 163466185}
  m_Layer: 0
  m_Name: LeftHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &163466185
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 163466184}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.23751105, w: 0.9713848}
  m_LocalPosition: {x: -0.032807138, y: 1.4210852e-16, z: 0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1005313729}
  m_Father: {fileID: 1238991691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &196230463
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 196230464}
  m_Layer: 6
  m_Name: AimHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &196230464
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 196230463}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1131331337}
  - {fileID: 67450125}
  - {fileID: 1772266649}
  - {fileID: 1499606741}
  m_Father: {fileID: 1023502522}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &200421604 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
    type: 3}
  m_PrefabInstance: {fileID: 398949001}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &203844586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 203844589}
  - component: {fileID: 203844588}
  - component: {fileID: 203844587}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &203844587
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203844586}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!108 &203844588
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203844586}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &203844589
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203844586}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &221709057
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 221709058}
  m_Layer: 0
  m_Name: CC_Base_UpperJaw
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &221709058
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221709057}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00005004184, y: 0.0000016683813, z: 1, w: 0.000009986938}
  m_LocalPosition: {x: 0.057560336, y: 0.018850708, z: 0.000037461232}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 640277526}
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &221890808
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 221890809}
  m_Layer: 0
  m_Name: lowerarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &221890809
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221890808}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020659559, y: -0.0000015906267, z: 0.00000052282104,
    w: 1}
  m_LocalPosition: {x: -0, y: 0.12450798, z: 0.00000007152557}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1313544555}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &233545466
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 233545467}
  m_Layer: 0
  m_Name: CC_Base_L_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &233545467
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233545466}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999708, y: 0.5000029, z: 0.49999705, w: 0.5000029}
  m_LocalPosition: {x: 0.06261507, y: 0.05849472, z: 0.031659685}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &234521621
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 234521622}
  m_Layer: 0
  m_Name: RightHandPinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &234521622
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 234521621}
  serializedVersion: 2
  m_LocalRotation: {x: 0.027015852, y: -0.19421314, z: -0.020509422, w: 0.9803728}
  m_LocalPosition: {x: 0.084097244, y: -0.01086027, z: 0.030482339}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 958973885}
  m_Father: {fileID: 1911527513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &236533410
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 236533411}
  - component: {fileID: 236533413}
  - component: {fileID: 236533412}
  m_Layer: 6
  m_Name: Aim Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &236533411
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236533410}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.7583194, y: 1.09, z: 13.26319}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &236533412
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236533410}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: aae886dd0d5d59844b4ec40cc2d96918, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &236533413
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236533410}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &236590511
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 236590512}
  m_Layer: 0
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &236590512
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236590511}
  serializedVersion: 2
  m_LocalRotation: {x: -0.18492702, y: -0.0000006674917, z: -0.0000000048566804, w: 0.98275226}
  m_LocalPosition: {x: 5.4267707e-15, y: 0.1061345, z: -6.039612e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1891208916}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &268200737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 268200738}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &268200738
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 268200737}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86622465, y: 0.018038174, z: -0.010375827, w: 0.49922118}
  m_LocalPosition: {x: 0.0030536088, y: 0.22314286, z: 0.11494481}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 500869698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &278449096
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 278449097}
  m_Layer: 21
  m_Name: Left Hand Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &278449097
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 278449096}
  serializedVersion: 2
  m_LocalRotation: {x: 0.25672767, y: -0.010788244, z: 0.79459333, w: -0.5500874}
  m_LocalPosition: {x: 0.028688993, y: -0.09583068, z: -0.004698406}
  m_LocalScale: {x: 0.9793076, y: 1.0065103, z: 1.0144148}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1110567227}
  m_LocalEulerAnglesHint: {x: 55.4101, y: -238.4923, z: -34.3354}
--- !u!1 &289717371
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 289717372}
  m_Layer: 0
  m_Name: pinky_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &289717372
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 289717371}
  serializedVersion: 2
  m_LocalRotation: {x: -0.007037891, y: 0.027214263, z: 0.3123169, w: -0.9495621}
  m_LocalPosition: {x: 0.00000015855768, y: 0.033094056, z: -0.00000008738882}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 532689012}
  m_Father: {fileID: 2071019250}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &302967735
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 302967736}
  - component: {fileID: 302967738}
  - component: {fileID: 302967737}
  m_Layer: 21
  m_Name: Laser2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &302967736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302967735}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000007, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1162545655}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!23 &302967737
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302967735}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &302967738
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302967735}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &311320239
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 311320240}
  m_Layer: 0
  m_Name: RightLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &311320240
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 311320239}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000760939, y: 0.000000007944512, z: -0.021634461, w: 0.99976593}
  m_LocalPosition: {x: 0.4070472, y: 0.00000003330736, z: 0.000000024850895}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1537216688}
  - {fileID: 1735682266}
  m_Father: {fileID: 1291548215}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &335438795
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 335438796}
  m_Layer: 0
  m_Name: RightHandPinky3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &335438796
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 335438795}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000014838336, y: -0.000000051789666, z: 0.23479164, w: 0.9720457}
  m_LocalPosition: {x: 0.017241538, y: 0.00000032920155, z: 0.000001548579}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 482453041}
  m_Father: {fileID: 958973885}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &350000797
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 350000798}
  m_Layer: 6
  m_Name: AimHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &350000798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 350000797}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1185579696}
  - {fileID: 122185267}
  - {fileID: 896993602}
  - {fileID: 46562587}
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &365752385
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 365752386}
  m_Layer: 0
  m_Name: spine_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &365752386
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 365752385}
  serializedVersion: 2
  m_LocalRotation: {x: 0.16194536, y: 0.000021845102, z: -0.00013518333, w: 0.9867998}
  m_LocalPosition: {x: 0.000016985054, y: 0.07044195, z: 0.018796988}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 791390003}
  m_Father: {fileID: 788567744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &391831347
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 391831348}
  m_Layer: 0
  m_Name: clavicle_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &391831348
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391831347}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0058371844, y: -0.17127489, z: 0.6711395, w: 0.72125083}
  m_LocalPosition: {x: -0.046346955, y: 0.21288395, z: 0.0042236745}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 922988970}
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &393862046
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 393862047}
  m_Layer: 0
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &393862047
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 393862046}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10251306, y: -0.0019429382, z: 0.026160479, w: 0.99438566}
  m_LocalPosition: {x: 0.00000012721283, y: 0.24901822, z: 0.000000119209275}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 493927424}
  - {fileID: 507680816}
  - {fileID: 2071019250}
  - {fileID: 2037831840}
  - {fileID: 439328758}
  - {fileID: 1074454516}
  m_Father: {fileID: 1313544555}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &398614087
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 398614088}
  m_Layer: 0
  m_Name: LeftHandMiddle4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &398614088
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 398614087}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.021994842, y: 7.105429e-17, z: 5.240252e-16}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 889734683}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &398949001
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1110567227}
    m_Modifications:
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 1.0152811
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9751786
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 1.0099491
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.13600597
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.020960063
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.0021262132
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70930266
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.01348043
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70448285
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.020296047
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6508003453768678230, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_Name
      value: AimPointer
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c513e3cb59a72543b45a4c740779f56, type: 3}
--- !u!1 &402353772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 402353773}
  m_Layer: 0
  m_Name: CC_Base_JawRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &402353773
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 402353772}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000026639605, y: -0.00006712115, z: 0.995976, w: 0.08962046}
  m_LocalPosition: {x: 0.026865957, y: 0.011196997, z: -0.00015041608}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 596914987}
  - {fileID: 1926856646}
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &418392750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 418392751}
  - component: {fileID: 418392752}
  m_Layer: 0
  m_Name: Middle_Ponytail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &418392751
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 418392750}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &418392752
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 418392750}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2d91b221636cd93469c2cff02bb387f9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1155122597573229543, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 791390003}
  - {fileID: 2036937282}
  - {fileID: 2084455373}
  - {fileID: 2117149252}
  - {fileID: 391831348}
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 2132946929}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 791390003}
  m_AABB:
    m_Center: {x: -0.0010192692, y: 0.46571648, z: -0.122491054}
    m_Extent: {x: 0.091920495, y: 0.17998058, z: 0.1596455}
  m_DirtyAABB: 0
--- !u!1 &426692019
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426692020}
  - component: {fileID: 426692021}
  m_Layer: 0
  m_Name: Ines_Body_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426692020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 426692019}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &426692021
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 426692019}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8de11d2c34af9584e98ad8fe854167f8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4083910945696752190, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1654331587}
  - {fileID: 1285479708}
  - {fileID: 1348073460}
  - {fileID: 1007695412}
  - {fileID: 1065482879}
  - {fileID: 500869698}
  - {fileID: 922988970}
  - {fileID: 1143128288}
  - {fileID: 2036937282}
  - {fileID: 391831348}
  - {fileID: 1729486767}
  - {fileID: 821742122}
  - {fileID: 2132946929}
  - {fileID: 659195098}
  - {fileID: 526572840}
  - {fileID: 788567744}
  - {fileID: 1427020990}
  - {fileID: 1862972380}
  - {fileID: 1639236270}
  - {fileID: 1313544555}
  - {fileID: 2084455373}
  - {fileID: 2117149252}
  - {fileID: 402353773}
  - {fileID: 818439002}
  - {fileID: 791390003}
  - {fileID: 652127906}
  - {fileID: 365752386}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 788567744}
  m_AABB:
    m_Center: {x: 0.0009224564, y: -0.20033973, z: 0.015904918}
    m_Extent: {x: 0.43948817, y: 0.78844875, z: 0.21551071}
  m_DirtyAABB: 0
--- !u!1 &433483582
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 433483583}
  m_Layer: 0
  m_Name: RightHandThumb4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &433483583
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433483582}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.025043922, y: 0.0000051662946, z: -0.0000018386571}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1872868590}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &435474584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 435474585}
  m_Layer: 0
  m_Name: LeftUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &435474585
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 435474584}
  serializedVersion: 2
  m_LocalRotation: {x: -0.46685073, y: -0.5240647, z: 0.50521725, w: 0.5021576}
  m_LocalPosition: {x: -0.09924337, y: -0.0000000018093348, z: -0.0000000018093338}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1817938615}
  - {fileID: 1729053173}
  m_Father: {fileID: 519551633}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &439328757
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 439328758}
  m_Layer: 0
  m_Name: thumb_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &439328758
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 439328757}
  serializedVersion: 2
  m_LocalRotation: {x: -0.40827867, y: 0.17583132, z: 0.21280256, w: -0.8701189}
  m_LocalPosition: {x: 0.008959771, y: 0.02282992, z: 0.018451856}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 129380941}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &441880493
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 441880494}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &441880494
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 441880493}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86621135, y: -0.017392404, z: 0.010017169, w: 0.4992747}
  m_LocalPosition: {x: -0.0029380997, y: 0.2230568, z: 0.11497639}
  m_LocalScale: {x: 1.0000001, y: 0.99999976, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1065482879}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &478356026
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 478356027}
  m_Layer: 0
  m_Name: LeftHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &478356027
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 478356026}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.016574945, w: 0.9998627}
  m_LocalPosition: {x: -0.03945325, y: -2.1316258e-16, z: 3.3750748e-16}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5686444}
  m_Father: {fileID: 1114303337}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &479941564
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 479941565}
  m_Layer: 0
  m_Name: RightHandMiddle4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &479941565
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 479941564}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000005102968, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.021995796, y: 0.000001947448, z: 0.000002485751}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2082184160}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &479989157
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 479989158}
  m_Layer: 0
  m_Name: index_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &479989158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 479989157}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000056894438, y: 0.012458745, z: 0.31955138, w: 0.94748706}
  m_LocalPosition: {x: 0.0000002079178, y: 0.03848414, z: 0.00000030916544}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 130749904}
  m_Father: {fileID: 2095647995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &481488593
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 481488594}
  m_Layer: 0
  m_Name: RightHandIndex4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &481488594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 481488593}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.023192393, y: 0.000002139012, z: 0.0000030417598}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1012358140}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &482453040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 482453041}
  m_Layer: 0
  m_Name: RightHandPinky4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &482453041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 482453040}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.014932987, y: 0.0000002966873, z: 0.000000012962931}
  m_LocalScale: {x: 0.9999997, y: 0.9999997, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 335438796}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &490280330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 490280331}
  m_Layer: 0
  m_Name: lowerarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &490280331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 490280330}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020261016, y: -0.0000023856562, z: -0.0000005890616,
    w: 1}
  m_LocalPosition: {x: 0.0000016784668, y: 0.12341133, z: -0.0000041913986}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1639236270}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &493927423
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 493927424}
  m_Layer: 0
  m_Name: index_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &493927424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 493927423}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0062812725, y: 0.04405264, z: 0.32261693, w: -0.9454831}
  m_LocalPosition: {x: 0.0016203529, y: 0.09508161, z: 0.017892258}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1030914214}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &500869697
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 500869698}
  m_Layer: 0
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &500869698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 500869697}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213837, y: -0.07327941, z: 0.035943866, w: 0.9615725}
  m_LocalPosition: {x: -0.00000042747706, y: 0.49376294, z: -0.0000005960465}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1522317842}
  - {fileID: 268200738}
  m_Father: {fileID: 1348073460}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &503412403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 503412404}
  - component: {fileID: 503412405}
  m_Layer: 9
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &503412404
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 503412403}
  serializedVersion: 2
  m_LocalRotation: {x: -0.15918235, y: 0.04981315, z: 0.22907774, w: 0.95901155}
  m_LocalPosition: {x: -0.013187794, y: -0.015286596, z: -0.0049534044}
  m_LocalScale: {x: 0.99999964, y: 0.99999964, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1899443445}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &503412405
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 503412403}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.2770516, y: 0.10494382, z: 0.10494382}
  m_Center: {x: 0.1000563, y: -0.029864434, z: 0.009991886}
--- !u!1 &507680815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 507680816}
  m_Layer: 0
  m_Name: middle_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &507680816
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 507680815}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03435428, y: 0.042340882, z: 0.3233633, w: -0.94470274}
  m_LocalPosition: {x: -0.00000030826786, y: 0.09293064, z: 0.000000016647395}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 709975675}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &510648139
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 510648140}
  m_Layer: 0
  m_Name: middle_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &510648140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 510648139}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012394886, y: 0.016453868, z: 0.31939703, w: 0.94739705}
  m_LocalPosition: {x: 0.0000005824259, y: 0.044478916, z: -0.00000023060784}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1823403080}
  m_Father: {fileID: 729983694}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &512595013
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 512595014}
  m_Layer: 0
  m_Name: RightHandRing4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &512595014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 512595013}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.020174485, y: 0.00000013908284, z: -0.0000008251726}
  m_LocalScale: {x: 1.0000002, y: 0.9999998, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2087275942}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &513596236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 513596237}
  m_Layer: 0
  m_Name: LeftShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &513596237
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 513596236}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0059826835, y: -0.10174823, z: 0.058391836, w: 0.99307704}
  m_LocalPosition: {x: -0.03546744, y: 0.19058922, z: 0.0026851227}
  m_LocalScale: {x: 1.000001, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2041163931}
  m_Father: {fileID: 1569415569}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &519551632
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 519551633}
  m_Layer: 0
  m_Name: Hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &519551633
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519551632}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.00000009052546, y: 0.92342365, z: 7.3968795e-15}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 435474585}
  - {fileID: 1291548215}
  - {fileID: 735305304}
  m_Father: {fileID: 854604475}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &525762876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 525762877}
  m_Layer: 0
  m_Name: RightToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &525762877
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 525762876}
  serializedVersion: 2
  m_LocalRotation: {x: 0.14476913, y: 0.12109891, z: 0.1857361, w: 0.96430236}
  m_LocalPosition: {x: 0.14710133, y: -0.0002619194, z: -0.0037131845}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1415666010}
  m_Father: {fileID: 1537216688}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &526572839
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 526572840}
  m_Layer: 0
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &526572840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 526572839}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0057723834, y: 0.25908446, z: 0.9656182, w: -0.020581087}
  m_LocalPosition: {x: -0.09741045, y: -0.021480039, z: -0.002206655}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1654331587}
  - {fileID: 659195098}
  m_Father: {fileID: 788567744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &532689011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 532689012}
  m_Layer: 0
  m_Name: pinky_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &532689012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 532689011}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0055833636, y: 0.025886443, z: 0.35945466, w: -0.93278676}
  m_LocalPosition: {x: -0.00000011170053, y: 0.0161742, z: -0.00000006224037}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 289717372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &533366487
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 533366488}
  m_Layer: 0
  m_Name: RightUpLegRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &533366488
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 533366487}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000089456805, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.21261396, y: -0.000000007625083, z: 0.00000007354678}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1291548215}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &549187623 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5562475231938505990, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c08dd31e7f694fdcaf3d250569c31167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &557736755
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 557736756}
  - component: {fileID: 557736757}
  m_Layer: 0
  m_Name: CC_Base_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &557736756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 557736755}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &557736757
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 557736755}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4372044fa75068540a70e02fce99869d, type: 2}
  - {fileID: 2100000, guid: 9eb706bdb64514d40aebd42dff2b7421, type: 2}
  - {fileID: 2100000, guid: a74d138b42e43c34aa7d8acec14b25f8, type: 2}
  - {fileID: 2100000, guid: e4995a92ac6380b4aa7bd9e587bdcee4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7654024864961597166, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 233545467}
  - {fileID: 2131469343}
  m_BlendShapeWeights:
  - 0
  - 0
  m_RootBone: {fileID: 2131469343}
  m_AABB:
    m_Center: {x: -0.031884596, y: -0.00096217636, z: -0.00013517216}
    m_Extent: {x: 0.048445284, y: 0.01696821, z: 0.01673663}
  m_DirtyAABB: 0
--- !u!1 &596914986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 596914987}
  m_Layer: 0
  m_Name: CC_Base_Teeth02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &596914987
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596914986}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00022650788, z: -0.00009729427, w: -0.00008053859}
  m_LocalPosition: {x: -0.024823641, y: 0.012739562, z: 0.00014736093}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 402353773}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &596950363
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 596950364}
  m_Layer: 0
  m_Name: thumb_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &596950364
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596950363}
  serializedVersion: 2
  m_LocalRotation: {x: -0.23369977, y: 0.0043788254, z: -0.0803165, w: 0.968976}
  m_LocalPosition: {x: 0.000000045634813, y: 0.026586972, z: 0.00000019371512}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1486610823}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &614345929
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 614345930}
  m_Layer: 0
  m_Name: RightHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &614345930
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614345929}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0120181525, y: 0.1032364, z: -0.2141118, w: 0.9712641}
  m_LocalPosition: {x: 0.03521302, y: -0.0000062512504, z: 0.0000021926287}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1872868590}
  m_Father: {fileID: 872981094}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &640277525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 640277526}
  m_Layer: 0
  m_Name: CC_Base_Teeth01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &640277526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 640277525}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00023426232, z: 0.00003416539, w: 0.000011044115}
  m_LocalPosition: {x: -0.0010065723, y: 0.0037287902, z: -0.000025710386}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 221709058}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &651974928
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 651974929}
  m_Layer: 0
  m_Name: LeftHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &651974929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 651974928}
  serializedVersion: 2
  m_LocalRotation: {x: -0.05855469, y: 0.005765164, z: 0.018105028, w: 0.9981034}
  m_LocalPosition: {x: -0.23482943, y: -0.0000001566176, z: 0.00000011444058}
  m_LocalScale: {x: 0.9999999, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1238991691}
  - {fileID: 1234249420}
  - {fileID: 1299817719}
  - {fileID: 1102329079}
  - {fileID: 1863733099}
  - {fileID: 2115578593}
  m_Father: {fileID: 943684963}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &652127905
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 652127906}
  m_Layer: 0
  m_Name: CC_Base_R_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &652127906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 652127905}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0010908963, y: 0.6153887, z: 0.78822196, w: -0.0013186043}
  m_LocalPosition: {x: 0.09821229, y: 0.047898404, z: 0.14247963}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &659195097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 659195098}
  m_Layer: 0
  m_Name: thigh_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &659195098
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659195097}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000008643836, y: 0.0000018727438, z: -6.0897634e-21, w: 1}
  m_LocalPosition: {x: -0.0000002813339, y: 0.23965019, z: -0.00000030994414}
  m_LocalScale: {x: 1.0000002, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 526572840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &659361830
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 659361831}
  m_Layer: 21
  m_Name: Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &659361831
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659361830}
  serializedVersion: 2
  m_LocalRotation: {x: 0.77128386, y: -0.026550846, z: 0.0323993, w: -0.6351116}
  m_LocalPosition: {x: -0.13594225, y: -0.02086684, z: 0.004009595}
  m_LocalScale: {x: 1.010579, y: 1.0150763, z: 0.9747495}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1110567227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &692375173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 692375174}
  m_Layer: 0
  m_Name: RightHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &692375174
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692375173}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000111498466, y: -0.0000000016098216, z: 0.20598526, w: 0.9785551}
  m_LocalPosition: {x: 0.029505037, y: -0.0000019048084, z: -0.0000017901558}
  m_LocalScale: {x: 1.0000002, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2087275942}
  m_Father: {fileID: 1479432771}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &709975674
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 709975675}
  m_Layer: 0
  m_Name: middle_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &709975675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 709975674}
  serializedVersion: 2
  m_LocalRotation: {x: -0.012596294, y: 0.020259678, z: 0.3189513, w: -0.9474709}
  m_LocalPosition: {x: -0.00000056589494, y: 0.044478264, z: -0.00000031473613}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 873247521}
  m_Father: {fileID: 507680816}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &712229566
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 712229567}
  m_Layer: 0
  m_Name: RightHandProp
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &712229567
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 712229566}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0724979, y: 0.020221079, z: -0.0037995602}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1911527513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &729983693
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 729983694}
  m_Layer: 0
  m_Name: middle_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &729983694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 729983693}
  serializedVersion: 2
  m_LocalRotation: {x: -0.035471946, y: 0.039232872, z: 0.32390818, w: 0.944609}
  m_LocalPosition: {x: -0.000000054016724, y: 0.09293024, z: 0.00000003667083}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 510648140}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &735305303
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 735305304}
  m_Layer: 0
  m_Name: Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &735305304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 735305303}
  serializedVersion: 2
  m_LocalRotation: {x: -0.028183335, y: 0.000000047389825, z: -0.000000039998238,
    w: 0.9996028}
  m_LocalPosition: {x: 0.000000019484096, y: 0.1172419, z: 1.3518631e-15}
  m_LocalScale: {x: 1.0000004, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1569415569}
  m_Father: {fileID: 519551633}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &740791347
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 740791348}
  m_Layer: 0
  m_Name: FemaleMovementAnimsetPro_1 Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &740791348
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 740791347}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.43168, y: -0, z: 5.82319}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1609099954}
  - {fileID: 1899443365}
  - {fileID: 1023502522}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &751332559
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 751332560}
  - component: {fileID: 751332562}
  - component: {fileID: 751332561}
  m_Layer: 21
  m_Name: Laser1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &751332560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 751332559}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 908534942}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!23 &751332561
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 751332559}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &751332562
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 751332559}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &756529699
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 756529700}
  - component: {fileID: 756529701}
  m_Layer: 9
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &756529700
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 756529699}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011307137, y: 0.0017547009, z: 0.23097293, w: 0.9728929}
  m_LocalPosition: {x: 0.0136398645, y: 0.011113342, z: 0.0005204335}
  m_LocalScale: {x: 0.9999996, y: 0.99999976, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1899443371}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &756529701
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 756529699}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.2770515, y: 0.10494381, z: 0.10494381}
  m_Center: {x: -0.09985237, y: 0.031586237, z: -0.0007496499}
--- !u!1 &764028469
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 764028470}
  m_Layer: 0
  m_Name: LeftHandPinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &764028470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 764028469}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.22453581, w: 0.97446585}
  m_LocalPosition: {x: -0.026565662, y: 0.000000076293944, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1891256920}
  m_Father: {fileID: 1299817719}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &788567743
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 788567744}
  m_Layer: 0
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &788567744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 788567743}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071064, y: 0.000010992124, z: -0.000016333257, w: 0.70710725}
  m_LocalPosition: {x: -0.0017271759, y: 0.013507819, z: -0.02622914}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 365752386}
  - {fileID: 526572840}
  - {fileID: 1862972380}
  m_Father: {fileID: 1751654721}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &788945575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 788945576}
  m_Layer: 21
  m_Name: AimTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &788945576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 788945575}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4837934, y: 0.51108474, z: -0.4920204, w: 0.51249623}
  m_LocalPosition: {x: -0.025851786, y: 0.00839515, z: 0.0016169167}
  m_LocalScale: {x: 1.0152817, y: 1.0099486, z: 0.9751789}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1162545655}
  m_Father: {fileID: 1074454516}
  m_LocalEulerAnglesHint: {x: -108.337, y: -265.796, z: 272.1}
--- !u!1 &791390002
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 791390003}
  m_Layer: 0
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &791390003
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 791390002}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12483237, y: 0.0000059753665, z: 0.0002518296, w: 0.9921778}
  m_LocalPosition: {x: 3.2196112e-10, y: 0.038068287, z: 0.00000032875678}
  m_LocalScale: {x: 1.0000001, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2036937282}
  m_Father: {fileID: 365752386}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &794541316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 794541317}
  m_Layer: 0
  m_Name: pinky_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &794541317
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 794541316}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0059519103, y: 0.022700317, z: 0.35952085, w: 0.93284196}
  m_LocalPosition: {x: 0.00000034226107, y: 0.016170712, z: -0.00000029638983}
  m_LocalScale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1777047158}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &816158918
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 816158919}
  - component: {fileID: 816158920}
  m_Layer: 0
  m_Name: Ines_Boots_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &816158919
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 816158918}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &816158920
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 816158918}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a2bce4765e81d974bb67c6b0b904ff40, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2213655293627143353, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1065482879}
  - {fileID: 1285479708}
  - {fileID: 1654331587}
  - {fileID: 500869698}
  - {fileID: 1007695412}
  - {fileID: 1348073460}
  - {fileID: 95360380}
  - {fileID: 1522317842}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1348073460}
  m_AABB:
    m_Center: {x: 0.07914308, y: 0.54150337, z: 0.0098627955}
    m_Extent: {x: 0.1488527, y: 0.14436929, z: 0.14782879}
  m_DirtyAABB: 0
--- !u!1 &818439001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 818439002}
  m_Layer: 0
  m_Name: CC_Base_L_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &818439002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 818439001}
  serializedVersion: 2
  m_LocalRotation: {x: -0.001101011, y: 0.6153887, z: 0.7882219, w: -0.0013287836}
  m_LocalPosition: {x: -0.098425545, y: 0.047770996, z: 0.14246082}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &821742121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 821742122}
  m_Layer: 0
  m_Name: upperarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &821742122
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821742121}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000019607223, y: -0.00000022029232, z: 0.0000012521631,
    w: 1}
  m_LocalPosition: {x: 0.00000045776366, y: 0.14070854, z: 0.0000006866455}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1729486767}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &823157132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 823157133}
  m_Layer: 0
  m_Name: RightForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &823157133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 823157132}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0025202748, y: 0.36401302, z: -0.011837274, w: 0.93131524}
  m_LocalPosition: {x: 0.2710439, y: 0.0000026377656, z: 0.00000057430935}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2002885918}
  - {fileID: 1911527513}
  m_Father: {fileID: 1204222773}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &849249592
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 849249593}
  m_Layer: 0
  m_Name: RightHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &849249593
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 849249592}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000112186406, y: 0.000000003898644, z: 0.23751105, w: 0.9713848}
  m_LocalPosition: {x: 0.032804545, y: -0.0000035324715, z: -0.00000602652}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1012358140}
  m_Father: {fileID: 2048931244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &854604474
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 854604475}
  - component: {fileID: 854604476}
  m_Layer: 0
  m_Name: Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &854604475
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 854604474}
  serializedVersion: 2
  m_LocalRotation: {x: -1.9106857e-15, y: -0.000000043711392, z: -0.00000004371139,
    w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 519551633}
  m_Father: {fileID: 1023502522}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &854604476
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 854604474}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e03e7ed42ce9470c899fc6cc550571e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hierarchy:
  - {fileID: 854604475}
  - {fileID: 519551633}
  - {fileID: 435474585}
  - {fileID: 1817938615}
  - {fileID: 1659492429}
  - {fileID: 1445606876}
  - {fileID: 1270341049}
  - {fileID: 1022431443}
  - {fileID: 1729053173}
  - {fileID: 1291548215}
  - {fileID: 311320240}
  - {fileID: 1537216688}
  - {fileID: 525762877}
  - {fileID: 1415666010}
  - {fileID: 1735682266}
  - {fileID: 533366488}
  - {fileID: 735305304}
  - {fileID: 1569415569}
  - {fileID: 513596237}
  - {fileID: 2041163931}
  - {fileID: 1031367421}
  - {fileID: 943684963}
  - {fileID: 1134862038}
  - {fileID: 651974929}
  - {fileID: 1238991691}
  - {fileID: 163466185}
  - {fileID: 1005313729}
  - {fileID: 1285847513}
  - {fileID: 1234249420}
  - {fileID: 1229944065}
  - {fileID: 889734683}
  - {fileID: 398614088}
  - {fileID: 1299817719}
  - {fileID: 764028470}
  - {fileID: 1891256920}
  - {fileID: 1366389261}
  - {fileID: 1102329079}
  - {fileID: 1863733099}
  - {fileID: 1492066494}
  - {fileID: 1296802776}
  - {fileID: 1214040597}
  - {fileID: 2115578593}
  - {fileID: 1114303337}
  - {fileID: 478356027}
  - {fileID: 5686444}
  - {fileID: 1891208916}
  - {fileID: 236590512}
  - {fileID: 1010048933}
  - {fileID: 1204222773}
  - {fileID: 1256626025}
  - {fileID: 823157133}
  - {fileID: 2002885918}
  - {fileID: 1911527513}
  - {fileID: 2048931244}
  - {fileID: 849249593}
  - {fileID: 1012358140}
  - {fileID: 481488594}
  - {fileID: 1525825665}
  - {fileID: 66172616}
  - {fileID: 2082184160}
  - {fileID: 479941565}
  - {fileID: 234521622}
  - {fileID: 958973885}
  - {fileID: 335438796}
  - {fileID: 482453041}
  - {fileID: 712229567}
  - {fileID: 1479432771}
  - {fileID: 692375174}
  - {fileID: 2087275942}
  - {fileID: 512595014}
  - {fileID: 872981094}
  - {fileID: 614345930}
  - {fileID: 1872868590}
  - {fileID: 433483583}
  _virtualElements: []
  _cachedHierarchyPose: []
  hierarchyDepths: 00000000010000000200000003000000040000000500000006000000040000000300000002000000030000000400000005000000060000000400000003000000020000000300000004000000050000000600000006000000070000000700000008000000090000000a0000000b00000008000000090000000a0000000b00000008000000090000000a0000000b0000000800000008000000090000000a0000000b00000008000000090000000a0000000b000000040000000500000004000000050000000600000006000000070000000700000008000000090000000a0000000b00000008000000090000000a0000000b00000008000000090000000a0000000b0000000800000008000000090000000a0000000b00000008000000090000000a0000000b000000
--- !u!1 &872981093
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 872981094}
  m_Layer: 0
  m_Name: RightHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &872981094
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872981093}
  serializedVersion: 2
  m_LocalRotation: {x: 0.80003554, y: 0.12284818, z: 0.07422333, w: 0.5825311}
  m_LocalPosition: {x: 0.011841082, y: 0.0088620465, z: -0.021983372}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 614345930}
  m_Father: {fileID: 1911527513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &873247520
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 873247521}
  m_Layer: 0
  m_Name: middle_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &873247521
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 873247520}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0054703737, y: 0.025574114, z: 0.33307493, w: -0.94253767}
  m_LocalPosition: {x: -0.000027199632, y: 0.028993504, z: 0.000005135887}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 709975675}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &877616362 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96268f2884c13d24eb4c6351a3388a7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &877616363 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c977cd4b40934cb887c285ed1f8476e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &889734682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 889734683}
  m_Layer: 0
  m_Name: LeftHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &889734683
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 889734682}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.27942947, w: 0.9601662}
  m_LocalPosition: {x: -0.025054876, y: -0.00000015258789, z: 2.84217e-16}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 398614088}
  m_Father: {fileID: 1229944065}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &896993601
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 896993602}
  - component: {fileID: 896993603}
  m_Layer: 6
  m_Name: LimbIK Left Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &896993602
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896993601}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &896993603
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896993601}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4db3c450680fd4c809d5ad90a2f24e5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: -0.67545736, y: 1.582675, z: -0.06562546}
    IKPositionWeight: 0
    root: {fileID: 896993602}
    target: {fileID: 0}
    IKRotationWeight: 0
    IKRotation: {x: 0.06974718, y: 0.07264444, z: 0.68527913, w: 0.72128403}
    bendNormal: {x: -0.00000012719387, y: -0.000000040853752, z: -0.0006869835}
    bone1:
      transform: {fileID: 922988970}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.00000015258789, y: 0.10289588, z: 0.0000000047683715}
      defaultLocalRotation: {x: 0.12092967, y: 0.0028766154, z: 0.029973214, w: 0.9922043}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone2:
      transform: {fileID: 1639236270}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.00000061035155, y: 0.2813307, z: -0.000000009536743}
      defaultLocalRotation: {x: -0.00000048121444, y: -0.000012135147, z: -0.004907392,
        w: 0.99998796}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone3:
      transform: {fileID: 1107498345}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.00000015258789, y: 0.24876311, z: -0.000000038146972}
      defaultLocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    goal: 2
    bendModifier: 0
    maintainRotationWeight: 0
    bendModifierWeight: 1
    bendGoal: {fileID: 0}
--- !u!1 &908534941
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 908534942}
  m_Layer: 21
  m_Name: Aim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &908534942
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 908534941}
  serializedVersion: 2
  m_LocalRotation: {x: -0.703968, y: -0.7074759, z: -0.040998396, w: 0.04718122}
  m_LocalPosition: {x: -0.016999971, y: 0.0030000117, z: 0.007999996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 751332560}
  - {fileID: 2060988120}
  m_Father: {fileID: 2032455264}
  m_LocalEulerAnglesHint: {x: -172.852, y: 0.5220032, z: -90.31702}
--- !u!1 &922988969
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 922988970}
  m_Layer: 0
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &922988970
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 922988969}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35353935, y: -0.033741195, z: 0.35527602, w: 0.8646678}
  m_LocalPosition: {x: 0.00000013085084, y: 0.102896124, z: 0.00000006216578}
  m_LocalScale: {x: 0.99999976, y: 0.9999998, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1639236270}
  - {fileID: 1143128288}
  m_Father: {fileID: 391831348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &943684962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 943684963}
  m_Layer: 0
  m_Name: LeftForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &943684963
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 943684962}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0025202748, y: 0.36401302, z: -0.011837274, w: 0.93131524}
  m_LocalPosition: {x: -0.2710414, y: -2.2737367e-15, z: 0.000000020774468}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1134862038}
  - {fileID: 651974929}
  m_Father: {fileID: 2041163931}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &958973884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 958973885}
  m_Layer: 0
  m_Name: RightHandPinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &958973885
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 958973884}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000108990534, y: 6.741975e-10, z: 0.22453581, w: 0.97446585}
  m_LocalPosition: {x: 0.026563896, y: -0.0000009486882, z: -0.0000015752777}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 335438796}
  m_Father: {fileID: 234521622}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &961739749
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 961739753}
  - component: {fileID: 961739752}
  - component: {fileID: 961739751}
  - component: {fileID: 961739750}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &961739750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!81 &961739751
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  m_Enabled: 1
--- !u!20 &961739752
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &961739753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1005313728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1005313729}
  m_Layer: 0
  m_Name: LeftHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1005313729
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005313728}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.22769472, w: 0.9737326}
  m_LocalPosition: {x: -0.022726024, y: -1.421085e-16, z: 1.421085e-16}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1285847513}
  m_Father: {fileID: 163466185}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1007695411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1007695412}
  m_Layer: 0
  m_Name: calf_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1007695412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1007695411}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000016143078, y: 0.0001604101, z: 0.000012576289, w: 1}
  m_LocalPosition: {x: -0.00000021934508, y: 0.24688132, z: -0.0000002670288}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1348073460}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1010048932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1010048933}
  m_Layer: 0
  m_Name: RightShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1010048933
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1010048932}
  serializedVersion: 2
  m_LocalRotation: {x: 0.993077, y: -0.058391813, z: -0.101748295, w: -0.005982555}
  m_LocalPosition: {x: 0.035467427, y: 0.19058868, z: 0.0026850742}
  m_LocalScale: {x: 1.000001, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1204222773}
  m_Father: {fileID: 1569415569}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1011972849
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1074454516}
    m_Modifications:
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 1.0152811
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9751786
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 1.0099491
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.13600597
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.020960063
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.0021262132
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70930266
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.01348043
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70448285
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.020296047
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6508003453768678230, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_Name
      value: AimPointer
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c513e3cb59a72543b45a4c740779f56, type: 3}
--- !u!4 &1011972850 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
    type: 3}
  m_PrefabInstance: {fileID: 1011972849}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1012358139
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1012358140}
  m_Layer: 0
  m_Name: RightHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1012358140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1012358139}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000015325229, y: -0.000000042147377, z: 0.22769472, w: 0.9737326}
  m_LocalPosition: {x: 0.022726359, y: -0.0000005041092, z: -0.0000008471723}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 481488594}
  m_Father: {fileID: 849249593}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1022431442
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1022431443}
  m_Layer: 0
  m_Name: LeftLegRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1022431443
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1022431442}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.25561854, y: 8.8817837e-17, z: 1.7763575e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1817938615}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1023502521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1023502522}
  - component: {fileID: 1023502524}
  - component: {fileID: 1023502523}
  - component: {fileID: 1023502525}
  - component: {fileID: 1023502528}
  - component: {fileID: 1023502527}
  - component: {fileID: 1023502526}
  m_Layer: 8
  m_Name: FemaleMovementAnimsetPro_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1023502522
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023502521}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 47061097}
  - {fileID: 854604475}
  - {fileID: 196230464}
  m_Father: {fileID: 740791348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1023502523
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023502521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdf51b00bdc44c699c58a3ed985775d4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  blendAsset: {fileID: 11400000, guid: 7f4ec5816c0a7324da6f3e2de5e3a44a, type: 2}
  forceUpdateWeights: 1
  alwaysAnimatePoses: 1
--- !u!95 &1023502524
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023502521}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &1023502525
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023502521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a9db18cd58f72348905fc8ae0a482d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  puppetMaster: {fileID: 1899443368}
  aimIKBeforePhysics: {fileID: 1131331339}
  target: {fileID: 236533411}
  fixAiming: 1
  fixLeftHand: 1
  aimIKAfterPhysics: {fileID: 67450126}
  hasLeftHandIK: 0
  leftHandIK: {fileID: 1772266650}
  leftHandTarget: {fileID: 1911527513}
  weight: 1
  targetSwitchSmoothTime: 0.3
  weightSmoothTime: 0.3
  smoothTurnTowardsTarget: 1
  maxRadiansDelta: 3
  maxMagnitudeDelta: 3
  slerpSpeed: 3
  smoothDampTime: 0
  pivotOffsetFromRoot: {x: 0, y: 1, z: 0}
  minDistance: 1
  offset: {x: 0, y: 0, z: 0}
  maxRootAngle: 45
  turnToTarget: 0
  turnToTargetTime: 0.2
  useAnimatedAimDirection: 0
  animatedAimDirection: {x: 0, y: 0, z: 1}
  m_currentAimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  animator: {fileID: 0}
--- !u!114 &1023502526
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023502521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d6ad7b53f86f9da4da426b673c422513, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animator: {fileID: 1023502524}
  _Transitions: {fileID: 0}
  _ActionOnDisable: 0
  _PlayAutomatically: 0
  _Animations: []
  _Controller:
    _FadeDuration: 0.25
    _Speed: 1
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Controller: {fileID: 9100000, guid: 816aaa7779881ee49920085b41a94e8c, type: 2}
    _ParameterBindings:
      _Mode: 0
      _Bindings: []
    _ActionsOnStop: 
  references:
    version: 2
    RefIds: []
--- !u!114 &1023502527
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023502521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab8cfa865e8923b45b97f2e6e5d21ce9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animancer: {fileID: 1023502526}
  _ActionMask: {fileID: 31900000, guid: 2ee86d3a5e2d3cf4fa965c906da1eb50, type: 2}
  _ActionFadeDuration: 0.25
  initializeMagicBlend: 0
  magicBlendComponent: {fileID: 0}
  enableMagicBlendBoneRebinding: 0
--- !u!114 &1023502528
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023502521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0816b76efdaf0f8499691c5103211fc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _AnimationManager: {fileID: 1023502527}
  m_currentUpperBodyAnimation:
    _FadeDuration: 0.25
    _Speed: 1
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _NormalizedStartTime: NaN
  references:
    version: 2
    RefIds: []
--- !u!1 &1030914213
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1030914214}
  m_Layer: 0
  m_Name: index_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1030914214
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1030914213}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0001571877, y: 0.016267497, z: 0.31922346, w: -0.9475398}
  m_LocalPosition: {x: -0.00000020908195, y: 0.038483683, z: 0.00000021420698}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1823914913}
  m_Father: {fileID: 493927424}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1031367420
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1031367421}
  m_Layer: 0
  m_Name: LeftArmRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1031367421
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1031367420}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000014945253, y: 6.617445e-24, z: 9.889939e-31, w: 1}
  m_LocalPosition: {x: -0.12099714, y: -0.000003967285, z: -0.00000091453967}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2041163931}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1036107749
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1036107750}
  - component: {fileID: 1036107751}
  m_Layer: 0
  m_Name: CC_Base_Teeth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1036107750
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036107749}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1036107751
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036107749}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: abfb25e4f694953438ae403c6e9d6024, type: 2}
  - {fileID: 2100000, guid: 6fa55045d8ab1c147a17e89a9c56285d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5727283436533813174, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 596914987}
  - {fileID: 640277526}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 640277526}
  m_AABB:
    m_Center: {x: -0.001902123, y: -0.018704455, z: 0.000000009313226}
    m_Extent: {x: 0.024316978, y: 0.01927523, z: 0.023962753}
  m_DirtyAABB: 0
--- !u!1 &1038214040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1038214041}
  - component: {fileID: 1038214042}
  m_Layer: 9
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1038214041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038214040}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86621135, y: -0.017392404, z: 0.010017169, w: 0.4992747}
  m_LocalPosition: {x: -0.0029380997, y: 0.2230568, z: 0.11497639}
  m_LocalScale: {x: 1.0000001, y: 0.99999976, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2057176480679072977}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1038214042
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038214040}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.12159323, y: 0.32100618, z: 0.12159323}
  m_Center: {x: -0.0031298567, y: 0.17521249, z: -0.045183767}
--- !u!1 &1056498518
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1056498519}
  - component: {fileID: 1056498520}
  m_Layer: 0
  m_Name: ToKo_Underwear_Bra_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1056498519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1056498518}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1056498520
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1056498518}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d013a4ba5c8aaa2458cdb9a205cd76fb, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7698357121477753718, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 652127906}
  - {fileID: 2036937282}
  - {fileID: 791390003}
  - {fileID: 818439002}
  - {fileID: 391831348}
  - {fileID: 2132946929}
  - {fileID: 922988970}
  - {fileID: 1729486767}
  - {fileID: 2084455373}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 791390003}
  m_AABB:
    m_Center: {x: -0.00050093234, y: 0.26455414, z: -0.025684223}
    m_Extent: {x: 0.16582833, y: 0.18954834, z: 0.17397897}
  m_DirtyAABB: 0
--- !u!1 &1065482878
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1065482879}
  m_Layer: 0
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1065482879
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1065482878}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242968, y: 0.07238963, z: -0.036033694, w: 0.96155715}
  m_LocalPosition: {x: 0.00000045076, y: 0.49370182, z: -0.00000068545336}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 95360380}
  - {fileID: 441880494}
  m_Father: {fileID: 1654331587}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1074454515
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 393862047}
    m_Modifications:
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_RootOrder
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.9897865
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 1.0257374
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.9850558
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.125
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.177
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.032
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.55991447
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.5254256
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.576247
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.27993417
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 15.412
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 257.036
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -72.423
      objectReference: {fileID: 0}
    - target: {fileID: 6360584711328263943, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_Name
      value: SciFi_Pistol_4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 788945576}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1950600341}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1011972850}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 2062405671}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c900257853a7a74f9b11e31d554c873, type: 3}
--- !u!4 &1074454516 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
    type: 3}
  m_PrefabInstance: {fileID: 1074454515}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1102329078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1102329079}
  m_Layer: 0
  m_Name: LeftHandProp
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1102329079
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1102329078}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.07249813, y: -0.020220336, z: 0.0037999724}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 651974929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1107498344
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1107498345}
  m_Layer: 0
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1107498345
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1107498344}
  serializedVersion: 2
  m_LocalRotation: {x: 0.100598276, y: 0.0018013357, z: -0.028034348, w: 0.9945305}
  m_LocalPosition: {x: 0.00000021723096, y: 0.24876517, z: -0.000000044703476}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2095647995}
  - {fileID: 729983694}
  - {fileID: 1775810478}
  - {fileID: 1708922341}
  - {fileID: 1770285347}
  m_Father: {fileID: 1639236270}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1110567227 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
    type: 3}
  m_PrefabInstance: {fileID: 14398074}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1114303336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1114303337}
  m_Layer: 0
  m_Name: LeftHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1114303337
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1114303336}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0120181525, y: 0.1032364, z: -0.2141118, w: 0.9712641}
  m_LocalPosition: {x: -0.03521252, y: -3.5527093e-17, z: -2.4868983e-16}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 478356027}
  m_Father: {fileID: 2115578593}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1131331336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1131331337}
  - component: {fileID: 1131331339}
  - component: {fileID: 1131331338}
  m_Layer: 6
  m_Name: AimIK Before Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1131331337
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131331336}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 196230464}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1131331338
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131331336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: Only bones that used as muscle targets by PuppetMaster should ba added to
    AimIK's "Bones".
--- !u!114 &1131331339
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131331336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: -4.3967533, y: -0.6735507, z: 7.8999414}
    IKPositionWeight: 0
    root: {fileID: 1131331337}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 1569415569}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -0.0000000055014895, y: 0.17185535, z: 0.0039671953}
      defaultLocalRotation: {x: -0.057572376, y: -0.00000003575419, z: -0.0000000013925856,
        w: 0.9983414}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 2032455264}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -4.3753185, y: -0.13609338, z: 3.691534}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &1134862037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1134862038}
  m_Layer: 0
  m_Name: LeftForeArmRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1134862038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134862037}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000018626448, y: 2.775557e-16, z: 0.000000014901161, w: 1}
  m_LocalPosition: {x: -0.14891969, y: 0.0000021362305, z: 0.0000022411346}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 943684963}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1143128287
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1143128288}
  m_Layer: 0
  m_Name: upperarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1143128288
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1143128287}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000017129787, y: 0.00000023047708, z: -0.0000014104878,
    w: 1}
  m_LocalPosition: {x: -0.00000030517577, y: 0.14066535, z: -0.0000000047683715}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 922988970}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1162545654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1162545655}
  m_Layer: 21
  m_Name: Aim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1162545655
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1162545654}
  serializedVersion: 2
  m_LocalRotation: {x: -0.703968, y: -0.7074759, z: -0.040998396, w: 0.04718122}
  m_LocalPosition: {x: -0.016999971, y: 0.0030000117, z: 0.007999996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1370722055}
  - {fileID: 302967736}
  m_Father: {fileID: 788945576}
  m_LocalEulerAnglesHint: {x: -172.852, y: 0.5220032, z: -90.31702}
--- !u!1 &1185579695
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1185579696}
  - component: {fileID: 1185579698}
  - component: {fileID: 1185579697}
  m_Layer: 6
  m_Name: AimIK Before Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1185579696
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185579695}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1185579697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185579695}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: Only bones that used as muscle targets by PuppetMaster should ba added to
    AimIK's "Bones".
--- !u!114 &1185579698
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185579695}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 1.234939, y: -1.4151461, z: 0.32664528}
    IKPositionWeight: 0
    root: {fileID: 1185579696}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 365752386}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
      defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268,
        w: 0.9867998}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 788945576}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -0.8694594, y: 1.5595894, z: 2.499767}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &1194285620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1194285621}
  m_Layer: 0
  m_Name: CC_Base_FacialBone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1194285621
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1194285620}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000017471123, y: -0.70710677, z: -0.0000017471124, w: 0.70710677}
  m_LocalPosition: {x: 0.0000000026177787, y: 0.0000003576279, z: 0.000000026078295}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 402353773}
  - {fileID: 233545467}
  - {fileID: 2131469343}
  - {fileID: 221709058}
  m_Father: {fileID: 2117149252}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1204222772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1204222773}
  m_Layer: 0
  m_Name: RightArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1204222773
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1204222772}
  serializedVersion: 2
  m_LocalRotation: {x: 0.016532818, y: 0.025041854, z: 0.32079405, w: 0.9466735}
  m_LocalPosition: {x: 0.11281534, y: -0.00000247362, z: -0.00000032867163}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1256626025}
  - {fileID: 823157133}
  m_Father: {fileID: 1010048933}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1214040596
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1214040597}
  m_Layer: 0
  m_Name: LeftHandRing4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1214040597
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1214040596}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.020174239, y: -0.000000009536743, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 0.9999998, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1296802776}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1218358741 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8691dd0ae36145d88c17f855c2eb91ba, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1218358743 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 88226de0bc8347118b6651808977535d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1229944064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1229944065}
  m_Layer: 0
  m_Name: LeftHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1229944065
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1229944064}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.19689088, w: 0.9804254}
  m_LocalPosition: {x: -0.033756983, y: -0.000000009536743, z: 0.00000015258789}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 889734683}
  m_Father: {fileID: 1234249420}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1234249419
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1234249420}
  m_Layer: 0
  m_Name: LeftHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1234249420
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1234249419}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0033880668, y: 0.006115769, z: 0.04840695, w: 0.99880326}
  m_LocalPosition: {x: -0.092778176, y: 0.016030323, z: 0.0037245366}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1229944065}
  m_Father: {fileID: 651974929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1238991690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1238991691}
  m_Layer: 0
  m_Name: LeftHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1238991691
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1238991690}
  serializedVersion: 2
  m_LocalRotation: {x: -0.005436919, y: 0.13635235, z: 0.06457102, w: 0.98853886}
  m_LocalPosition: {x: -0.091047734, y: 0.013269413, z: 0.022524143}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 163466185}
  m_Father: {fileID: 651974929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1253068707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1253068708}
  m_Layer: 0
  m_Name: thumb_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1253068708
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1253068707}
  serializedVersion: 2
  m_LocalRotation: {x: 0.23376386, y: 0.0045397724, z: -0.08019013, w: -0.9689703}
  m_LocalPosition: {x: -0.00000011175872, y: 0.026587524, z: 0.00000024400651}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 129380941}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1256626024
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1256626025}
  m_Layer: 0
  m_Name: RightArmRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1256626025
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1256626024}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000014945253, y: 6.617445e-24, z: 9.889939e-31, w: 1}
  m_LocalPosition: {x: 0.12100176, y: 0.000008566627, z: 0.000001876351}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1204222773}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1270341048
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1270341049}
  m_Layer: 0
  m_Name: LeftToeBase_END
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1270341049
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1270341048}
  serializedVersion: 2
  m_LocalRotation: {x: -9.9503475e-11, y: -0.000000012766207, z: -1.2702819e-18, w: 1}
  m_LocalPosition: {x: -0.06786768, y: -0.000000009536743, z: -0.000000009536743}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1445606876}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1285479707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1285479708}
  m_Layer: 0
  m_Name: calf_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1285479708
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285479707}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000035669185, y: -0.000019150324, z: -0.000027639298, w: 1}
  m_LocalPosition: {x: 0.00000021934508, y: 0.24685065, z: -0.00000035762787}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1654331587}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1285847512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1285847513}
  m_Layer: 0
  m_Name: LeftHandIndex4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1285847513
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285847512}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.023191687, y: -0.000000038146972, z: 4.618525e-16}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1005313729}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1291548214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1291548215}
  m_Layer: 0
  m_Name: RightUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1291548215
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1291548214}
  serializedVersion: 2
  m_LocalRotation: {x: -0.50215745, y: 0.50521725, z: 0.5240648, w: -0.46685067}
  m_LocalPosition: {x: 0.09924341, y: 0.00000026855878, z: -0.000000019161634}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 311320240}
  - {fileID: 533366488}
  m_Father: {fileID: 519551633}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1296802775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1296802776}
  m_Layer: 0
  m_Name: LeftHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1296802776
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1296802775}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.23595853, w: 0.97176313}
  m_LocalPosition: {x: -0.020535992, y: -0.000000076293944, z: -0.00000015258789}
  m_LocalScale: {x: 0.9999995, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1214040597}
  m_Father: {fileID: 1492066494}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1299817718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1299817719}
  m_Layer: 0
  m_Name: LeftHandPinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1299817719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1299817718}
  serializedVersion: 2
  m_LocalRotation: {x: 0.027647855, y: -0.17573705, z: -0.013279653, w: 0.9839592}
  m_LocalPosition: {x: -0.08409535, y: 0.01086274, z: -0.030479006}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 764028470}
  m_Father: {fileID: 651974929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1313544554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1313544555}
  m_Layer: 0
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1313544555
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313544554}
  serializedVersion: 2
  m_LocalRotation: {x: 0.64271826, y: 0.005757572, z: 0.005592018, w: 0.76606065}
  m_LocalPosition: {x: 0.0000007450582, y: 0.2814179, z: 0.00000092387234}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 393862047}
  - {fileID: 221890809}
  m_Father: {fileID: 1729486767}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1314009586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1314009587}
  - component: {fileID: 1314009588}
  m_Layer: 0
  m_Name: CC_Base_Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1314009587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314009586}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1314009588
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314009586}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 231f293b4d8f36f46bf37186c629de1e, type: 2}
  - {fileID: 2100000, guid: ed2b5a2049d359c4bbf89fd1847dddb0, type: 2}
  - {fileID: 2100000, guid: 2ad698a8dad45fc4a8d37c31b58dcdf5, type: 2}
  - {fileID: 2100000, guid: ee7c162b70ff995438710e2235472f8c, type: 2}
  - {fileID: 2100000, guid: 7cdfc14f5abeb8d4d93f1cc553af6919, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2988033852023935963, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1639236270}
  - {fileID: 490280331}
  - {fileID: 788567744}
  - {fileID: 791390003}
  - {fileID: 365752386}
  - {fileID: 2036937282}
  - {fileID: 2117149252}
  - {fileID: 402353773}
  - {fileID: 2084455373}
  - {fileID: 526572840}
  - {fileID: 1862972380}
  - {fileID: 1143128288}
  - {fileID: 922988970}
  - {fileID: 1654331587}
  - {fileID: 1285479708}
  - {fileID: 1065482879}
  - {fileID: 659195098}
  - {fileID: 1107498345}
  - {fileID: 729983694}
  - {fileID: 510648140}
  - {fileID: 2095647995}
  - {fileID: 1823403080}
  - {fileID: 1708922341}
  - {fileID: 479989158}
  - {fileID: 130749904}
  - {fileID: 1657561067}
  - {fileID: 1681152083}
  - {fileID: 1775810478}
  - {fileID: 1777047158}
  - {fileID: 794541317}
  - {fileID: 1770285347}
  - {fileID: 1486610823}
  - {fileID: 596950364}
  - {fileID: 95360380}
  - {fileID: 391831348}
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 2132946929}
  - {fileID: 1313544555}
  - {fileID: 221890809}
  - {fileID: 821742122}
  - {fileID: 1729486767}
  - {fileID: 1348073460}
  - {fileID: 1007695412}
  - {fileID: 500869698}
  - {fileID: 1427020990}
  - {fileID: 507680816}
  - {fileID: 709975675}
  - {fileID: 393862047}
  - {fileID: 493927424}
  - {fileID: 873247521}
  - {fileID: 2037831840}
  - {fileID: 1030914214}
  - {fileID: 1823914913}
  - {fileID: 1331996526}
  - {fileID: 123809142}
  - {fileID: 2071019250}
  - {fileID: 289717372}
  - {fileID: 532689012}
  - {fileID: 439328758}
  - {fileID: 129380941}
  - {fileID: 1253068708}
  - {fileID: 1522317842}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 788567744}
  m_AABB:
    m_Center: {x: 0.00039353967, y: 0.5694388, z: 0.004483696}
    m_Extent: {x: 0.8668691, y: 0.162474, z: 0.102908105}
  m_DirtyAABB: 0
--- !u!1 &1331996525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1331996526}
  m_Layer: 0
  m_Name: ring_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1331996526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1331996525}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0028674062, y: 0.020136401, z: 0.31277582, w: -0.9496092}
  m_LocalPosition: {x: 0.00000019930307, y: 0.042684704, z: -0.000000009495128}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 123809142}
  m_Father: {fileID: 2037831840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1347422853
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1347422854}
  m_Layer: 0
  m_Name: CC_Base_Tongue02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1347422854
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1347422853}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0011598348, y: -0.005633796, z: -0.09942047, w: 0.9950289}
  m_LocalPosition: {x: -0.009785385, y: 0.0002241516, z: -0.0000043535233}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1699357223}
  m_Father: {fileID: 1926856646}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1348073459
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1348073460}
  m_Layer: 0
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1348073460
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348073459}
  serializedVersion: 2
  m_LocalRotation: {x: -0.6465417, y: -0.0132559575, z: 0.018137911, w: 0.76254785}
  m_LocalPosition: {x: 0.00000021420419, y: 0.47928715, z: -0.00000023841855}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1007695412}
  - {fileID: 500869698}
  m_Father: {fileID: 1862972380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1366389260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1366389261}
  m_Layer: 0
  m_Name: LeftHandPinky4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1366389261
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1366389260}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.014932311, y: 0.00000014305114, z: -5.3290678e-17}
  m_LocalScale: {x: 0.9999997, y: 0.9999997, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1891256920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1370722054
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1370722055}
  - component: {fileID: 1370722057}
  - component: {fileID: 1370722056}
  m_Layer: 21
  m_Name: Laser1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1370722055
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370722054}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1162545655}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!23 &1370722056
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370722054}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1370722057
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370722054}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &1374703566 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aaf09bfab896407fa91b5c272c57cb0d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1415666009
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1415666010}
  m_Layer: 0
  m_Name: RightToeBase_END
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1415666010
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1415666009}
  serializedVersion: 2
  m_LocalRotation: {x: 0.30043983, y: -0.012190913, z: 0.008527296, w: 0.95368475}
  m_LocalPosition: {x: 0.067837566, y: 0.0006067409, z: 0.0019259243}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 525762877}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1427020989
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1427020990}
  m_Layer: 0
  m_Name: thigh_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1427020990
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1427020989}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000012267265, y: -0.0000017183896, z: 0.00000011269007,
    w: 1}
  m_LocalPosition: {x: 0.00000012397766, y: 0.2396434, z: -0.00000011920929}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1862972380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1445606875
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1445606876}
  m_Layer: 0
  m_Name: LeftToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1445606876
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445606875}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.21290359, w: 0.9770732}
  m_LocalPosition: {x: -0.14714848, y: -1.7763566e-17, z: -2.6645352e-17}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1270341049}
  m_Father: {fileID: 1659492429}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1476540753 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0dcbffa32d899b4cac41b83a61cef7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1479432770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1479432771}
  m_Layer: 0
  m_Name: RightHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1479432771
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479432770}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03353324, y: -0.103247866, z: -0.019998573, w: 0.99388903}
  m_LocalPosition: {x: 0.09002867, y: -0.01441745, z: 0.014309885}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 692375174}
  m_Father: {fileID: 1911527513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1486610822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1486610823}
  m_Layer: 0
  m_Name: thumb_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1486610823
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486610822}
  serializedVersion: 2
  m_LocalRotation: {x: -0.21795404, y: 0.0152288675, z: -0.06782591, w: 0.9734802}
  m_LocalPosition: {x: 0.0000007729979, y: 0.054367814, z: -0.0000007720665}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 596950364}
  m_Father: {fileID: 1770285347}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1492066493
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1492066494}
  m_Layer: 0
  m_Name: LeftHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1492066494
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1492066493}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.20598526, w: 0.9785551}
  m_LocalPosition: {x: -0.02950631, y: -0.000000085830685, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1296802776}
  m_Father: {fileID: 1863733099}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1499606740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1499606741}
  m_Layer: 6
  m_Name: ForwardAimTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1499606741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1499606740}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.049999952, y: 1.09, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 196230464}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1522317841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1522317842}
  m_Layer: 0
  m_Name: ball_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1522317842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1522317841}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4711399, y: 0.02005827, z: -0.02584058, w: 0.8814517}
  m_LocalPosition: {x: 0.000004895031, y: 0.14396676, z: 0.000001594424}
  m_LocalScale: {x: 0.9999997, y: 0.99999964, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 500869698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1525825664
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1525825665}
  m_Layer: 0
  m_Name: RightHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1525825665
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525825664}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0038476975, y: 0.0058376086, z: 0.124943234, w: 0.9921393}
  m_LocalPosition: {x: 0.09277832, y: -0.016029581, z: -0.003724617}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 66172616}
  m_Father: {fileID: 1911527513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1537216687
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1537216688}
  m_Layer: 0
  m_Name: RightFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1537216688
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1537216687}
  serializedVersion: 2
  m_LocalRotation: {x: 0.13248642, y: 0.12472641, z: 0.5498335, w: 0.81521404}
  m_LocalPosition: {x: 0.43269977, y: 0.000000017737898, z: 0.0000000031079455}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 525762877}
  - {fileID: 1635058247}
  m_Father: {fileID: 311320240}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1550469829
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1550469830}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1550469830
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1550469829}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011307137, y: 0.0017547009, z: 0.23097293, w: 0.9728929}
  m_LocalPosition: {x: 0.0136398645, y: 0.011113342, z: 0.0005204335}
  m_LocalScale: {x: 0.9999996, y: 0.99999976, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1659492429}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1569415568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1569415569}
  m_Layer: 0
  m_Name: Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1569415569
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1569415568}
  serializedVersion: 2
  m_LocalRotation: {x: -0.057572376, y: -0.00000003575419, z: -0.0000000013925856,
    w: 0.9983414}
  m_LocalPosition: {x: -0.0000000055014895, y: 0.17185535, z: 0.0039671953}
  m_LocalScale: {x: 0.99999934, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 513596237}
  - {fileID: 1891208916}
  - {fileID: 1010048933}
  m_Father: {fileID: 735305304}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1603960883
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1603960884}
  - component: {fileID: 1603960890}
  - component: {fileID: 1603960889}
  - component: {fileID: 1603960888}
  - component: {fileID: 1603960887}
  - component: {fileID: 1603960886}
  - component: {fileID: 1603960885}
  m_Layer: 8
  m_Name: Female1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1603960884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1314009587}
  - {fileID: 557736756}
  - {fileID: 1036107750}
  - {fileID: 5386603}
  - {fileID: 426692020}
  - {fileID: 816158919}
  - {fileID: 1634441398}
  - {fileID: 418392751}
  - {fileID: 1751654721}
  - {fileID: 2059762510}
  - {fileID: 1056498519}
  - {fileID: 350000798}
  m_Father: {fileID: 2060465284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1603960885
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab8cfa865e8923b45b97f2e6e5d21ce9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animancer: {fileID: 1603960886}
  _ActionMask: {fileID: 101100000, guid: 8705d95a91de64f5b920df36d2a781ef, type: 2}
  _ActionFadeDuration: 0.25
  initializeMagicBlend: 0
  magicBlendComponent: {fileID: 0}
  enableMagicBlendBoneRebinding: 0
--- !u!114 &1603960886
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d6ad7b53f86f9da4da426b673c422513, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animator: {fileID: 1603960890}
  _Transitions: {fileID: 0}
  _ActionOnDisable: 0
  _PlayAutomatically: 0
  _Animations: []
  _Controller:
    _FadeDuration: 0.25
    _Speed: 1
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Controller: {fileID: 9100000, guid: 816aaa7779881ee49920085b41a94e8c, type: 2}
    _ParameterBindings:
      _Mode: 0
      _Bindings: []
    _ActionsOnStop: 
  references:
    version: 2
    RefIds: []
--- !u!114 &1603960887
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0816b76efdaf0f8499691c5103211fc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _AnimationManager: {fileID: 1603960885}
  m_currentUpperBodyAnimation:
    _FadeDuration: 0.25
    _Speed: 1
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _NormalizedStartTime: NaN
  references:
    version: 2
    RefIds: []
--- !u!114 &1603960888
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a9db18cd58f72348905fc8ae0a482d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  puppetMaster: {fileID: 3382561355986842853}
  aimIKBeforePhysics: {fileID: 1185579698}
  target: {fileID: 236533411}
  fixAiming: 1
  fixLeftHand: 1
  aimIKAfterPhysics: {fileID: 122185268}
  hasLeftHandIK: 0
  leftHandIK: {fileID: 896993603}
  leftHandTarget: {fileID: 2062405671}
  weight: 1
  targetSwitchSmoothTime: 0.3
  weightSmoothTime: 0.3
  smoothTurnTowardsTarget: 1
  maxRadiansDelta: 3
  maxMagnitudeDelta: 3
  slerpSpeed: 3
  smoothDampTime: 0
  pivotOffsetFromRoot: {x: 0, y: 1, z: 0}
  minDistance: 1
  offset: {x: 0, y: 0, z: 0}
  maxRootAngle: 45
  turnToTarget: 0
  turnToTargetTime: 0.2
  useAnimatedAimDirection: 0
  animatedAimDirection: {x: 0, y: 0, z: 1}
  m_currentAimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  animator: {fileID: 0}
--- !u!114 &1603960889
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdf51b00bdc44c699c58a3ed985775d4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  blendAsset: {fileID: 11400000, guid: 6fcc1e3faff94884af6bac841671ac98, type: 2}
  forceUpdateWeights: 1
  alwaysAnimatePoses: 1
--- !u!95 &1603960890
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &1609099953
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1609099954}
  - component: {fileID: 1609099955}
  m_Layer: 0
  m_Name: Behaviours
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1609099954
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1609099953}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 740791348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1609099955
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1609099953}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: All Puppet Behaviours should be parented to this GameObject, the PuppetMaster
    will automatically find them from here. All Puppet Behaviours have been designed
    so that they could be simply copied from one character to another without changing
    any references. It is important because they contain a lot of parameters and
    would be otherwise tedious to set up and tweak.
--- !u!1 &1634441397
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1634441398}
  - component: {fileID: 1634441399}
  m_Layer: 0
  m_Name: Ines_Default_Gloves
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1634441398
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1634441397}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1634441399
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1634441397}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 622c22f36d9b9c94d89d6f00a899b550, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 279771734379647476, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1708922341}
  - {fileID: 1657561067}
  - {fileID: 1107498345}
  - {fileID: 1775810478}
  - {fileID: 1777047158}
  - {fileID: 729983694}
  - {fileID: 490280331}
  - {fileID: 2095647995}
  - {fileID: 1770285347}
  - {fileID: 1639236270}
  - {fileID: 1486610823}
  - {fileID: 479989158}
  - {fileID: 510648140}
  - {fileID: 2037831840}
  - {fileID: 1331996526}
  - {fileID: 393862047}
  - {fileID: 2071019250}
  - {fileID: 289717372}
  - {fileID: 507680816}
  - {fileID: 221890809}
  - {fileID: 493927424}
  - {fileID: 439328758}
  - {fileID: 1313544555}
  - {fileID: 129380941}
  - {fileID: 1030914214}
  - {fileID: 709975675}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1313544555}
  m_AABB:
    m_Center: {x: 0.0060192235, y: -0.42701393, z: 0.025629144}
    m_Extent: {x: 0.041894034, y: 0.81338686, z: 0.07448232}
  m_DirtyAABB: 0
--- !u!1 &1635058246
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1635058247}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1635058247
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635058246}
  serializedVersion: 2
  m_LocalRotation: {x: -0.15918235, y: 0.04981315, z: 0.22907774, w: 0.95901155}
  m_LocalPosition: {x: -0.013187794, y: -0.015286596, z: -0.0049534044}
  m_LocalScale: {x: 0.99999964, y: 0.99999964, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1537216688}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1639236269
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1639236270}
  m_Layer: 0
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1639236270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639236269}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6427098, y: -0.006489644, z: -0.0037715663, w: 0.76607305}
  m_LocalPosition: {x: -0.0000005960465, y: 0.2813315, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1107498345}
  - {fileID: 490280331}
  m_Father: {fileID: 922988970}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1654331586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1654331587}
  m_Layer: 0
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1654331587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1654331586}
  serializedVersion: 2
  m_LocalRotation: {x: -0.646798, y: 0.013238879, z: -0.016659563, w: 0.7623645}
  m_LocalPosition: {x: -0.00000054202985, y: 0.47930062, z: -0.0000006407499}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1285479708}
  - {fileID: 1065482879}
  m_Father: {fileID: 526572840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1657561066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1657561067}
  m_Layer: 0
  m_Name: ring_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1657561067
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1657561066}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0012214995, y: 0.016848808, z: 0.31292173, w: 0.9496287}
  m_LocalPosition: {x: 0.00000055145944, y: 0.041719615, z: -0.00000071620445}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1681152083}
  m_Father: {fileID: 1708922341}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1659082871
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1659082872}
  - component: {fileID: 1659082873}
  m_Layer: 0
  m_Name: Behaviours
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1659082872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1659082871}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2060465284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1659082873
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1659082871}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: All Puppet Behaviours should be parented to this GameObject, the PuppetMaster
    will automatically find them from here. All Puppet Behaviours have been designed
    so that they could be simply copied from one character to another without changing
    any references. It is important because they contain a lot of parameters and
    would be otherwise tedious to set up and tweak.
--- !u!1 &1659492428
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1659492429}
  m_Layer: 0
  m_Name: LeftFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1659492429
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1659492428}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0023852196, y: 0.048043612, z: 0.5650304, w: 0.8236667}
  m_LocalPosition: {x: -0.43269962, y: 0.00000001069738, z: -0.000000010534252}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1445606876}
  - {fileID: 1550469830}
  m_Father: {fileID: 1817938615}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1681152082
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1681152083}
  m_Layer: 0
  m_Name: ring_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1681152083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1681152082}
  serializedVersion: 2
  m_LocalRotation: {x: 0.001411002, y: 0.017358664, z: 0.33464137, w: 0.9421846}
  m_LocalPosition: {x: -0.00000051769905, y: 0.026329383, z: 0.000000035463703}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1657561067}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1699357222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1699357223}
  m_Layer: 0
  m_Name: CC_Base_Tongue03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1699357223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1699357222}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000046749043, y: -0.0010550104, z: 0.11461715, w: 0.9934092}
  m_LocalPosition: {x: -0.013736267, y: 0.000017242432, z: 0.00000092945993}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1347422854}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1708922340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1708922341}
  m_Layer: 0
  m_Name: ring_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1708922341
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708922340}
  serializedVersion: 2
  m_LocalRotation: {x: -0.109960675, y: 0.03511855, z: 0.3183628, w: 0.94091475}
  m_LocalPosition: {x: -0.0015324334, y: 0.088897504, z: -0.01653782}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1657561067}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1729053172
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1729053173}
  m_Layer: 0
  m_Name: LeftUpLegRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1729053173
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1729053172}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000089456805, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.21261406, y: 4.440892e-18, z: -1.7763568e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 435474585}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1729486766
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1729486767}
  m_Layer: 0
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1729486767
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1729486766}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35020316, y: 0.03190345, z: -0.35329235, w: 0.8669052}
  m_LocalPosition: {x: -0.00000013830142, y: 0.100599766, z: -0.00000043236645}
  m_LocalScale: {x: 0.99999976, y: 0.99999964, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1313544555}
  - {fileID: 821742122}
  m_Father: {fileID: 2132946929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1735682265
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1735682266}
  m_Layer: 0
  m_Name: RightLegRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1735682266
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1735682265}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.2556185, y: -0.000000030411343, z: 0.000000016188972}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 311320240}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1751654720
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1751654721}
  - component: {fileID: 1751654722}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1751654721
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1751654720}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0.0000029843027, y: 0, z: -0.03379729}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 788567744}
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1751654722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1751654720}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e03e7ed42ce9470c899fc6cc550571e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hierarchy:
  - {fileID: 1751654721}
  - {fileID: 788567744}
  - {fileID: 365752386}
  - {fileID: 791390003}
  - {fileID: 2036937282}
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 391831348}
  - {fileID: 922988970}
  - {fileID: 1639236270}
  - {fileID: 1107498345}
  - {fileID: 2095647995}
  - {fileID: 479989158}
  - {fileID: 130749904}
  - {fileID: 729983694}
  - {fileID: 510648140}
  - {fileID: 1823403080}
  - {fileID: 1775810478}
  - {fileID: 1777047158}
  - {fileID: 794541317}
  - {fileID: 1708922341}
  - {fileID: 1657561067}
  - {fileID: 1681152083}
  - {fileID: 1770285347}
  - {fileID: 1486610823}
  - {fileID: 596950364}
  - {fileID: 490280331}
  - {fileID: 1143128288}
  - {fileID: 2132946929}
  - {fileID: 1729486767}
  - {fileID: 1313544555}
  - {fileID: 393862047}
  - {fileID: 493927424}
  - {fileID: 1030914214}
  - {fileID: 1823914913}
  - {fileID: 507680816}
  - {fileID: 709975675}
  - {fileID: 873247521}
  - {fileID: 2071019250}
  - {fileID: 289717372}
  - {fileID: 532689012}
  - {fileID: 2037831840}
  - {fileID: 1331996526}
  - {fileID: 123809142}
  - {fileID: 439328758}
  - {fileID: 129380941}
  - {fileID: 1253068708}
  - {fileID: 221890809}
  - {fileID: 821742122}
  - {fileID: 2084455373}
  - {fileID: 2117149252}
  - {fileID: 1194285621}
  - {fileID: 402353773}
  - {fileID: 596914987}
  - {fileID: 1926856646}
  - {fileID: 1347422854}
  - {fileID: 1699357223}
  - {fileID: 233545467}
  - {fileID: 2131469343}
  - {fileID: 221709058}
  - {fileID: 640277526}
  - {fileID: 526572840}
  - {fileID: 1654331587}
  - {fileID: 1285479708}
  - {fileID: 1065482879}
  - {fileID: 95360380}
  - {fileID: 659195098}
  - {fileID: 1862972380}
  - {fileID: 1348073460}
  - {fileID: 1007695412}
  - {fileID: 500869698}
  - {fileID: 1522317842}
  - {fileID: 1427020990}
  _virtualElements: []
  _cachedHierarchyPose: []
  hierarchyDepths: 0000000001000000020000000300000004000000050000000500000005000000060000000700000008000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000080000000700000005000000060000000700000008000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b00000008000000070000000500000006000000070000000800000009000000090000000a0000000b00000008000000080000000800000009000000020000000300000004000000040000000500000003000000020000000300000004000000040000000500000003000000
--- !u!1 &1770285346
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1770285347}
  m_Layer: 0
  m_Name: thumb_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1770285347
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1770285346}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40757298, y: 0.17505525, z: 0.21308547, w: 0.87053686}
  m_LocalPosition: {x: -0.008979087, y: 0.022824226, z: 0.018420167}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1486610823}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &1771705685
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014273}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 0}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 2
  m_YMotion: 2
  m_ZMotion: 2
  m_AngularXMotion: 2
  m_AngularYMotion: 2
  m_AngularZMotion: 2
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!65 &1771705686
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014273}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.23228455, y: 0.37822387, z: 0.13937074}
  m_Center: {x: -5.3642424e-10, y: 0.11719252, z: -0.0010104179}
--- !u!1 &1772266648
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1772266649}
  - component: {fileID: 1772266650}
  m_Layer: 6
  m_Name: LimbIK Left Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1772266649
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1772266648}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 196230464}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1772266650
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1772266648}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4db3c450680fd4c809d5ad90a2f24e5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: -6.916598, y: 1.0956056, z: 5.92056}
    IKPositionWeight: 0
    root: {fileID: 1772266649}
    target: {fileID: 0}
    IKRotationWeight: 0
    IKRotation: {x: -0.26785, y: 0.28765196, z: 0.34181583, w: 0.8536245}
    bendNormal: {x: -0.034241438, y: 0.04972081, z: -0.0016488314}
    bone1:
      transform: {fileID: 513596237}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.03546744, y: 0.19058922, z: 0.0026851227}
      defaultLocalRotation: {x: 0.0059826835, y: -0.10174823, z: 0.058391836, w: 0.99307704}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone2:
      transform: {fileID: 943684963}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.2710414, y: -2.2737367e-15, z: 0.000000020774468}
      defaultLocalRotation: {x: 0.0025202748, y: 0.36401302, z: -0.011837274, w: 0.93131524}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone3:
      transform: {fileID: 651974929}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.23482943, y: -0.0000001566176, z: 0.00000011444058}
      defaultLocalRotation: {x: -0.05855469, y: 0.005765164, z: 0.018105028, w: 0.9981034}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    goal: 2
    bendModifier: 0
    maintainRotationWeight: 0
    bendModifierWeight: 1
    bendGoal: {fileID: 0}
--- !u!1 &1775810477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1775810478}
  m_Layer: 0
  m_Name: pinky_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1775810478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1775810477}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14139149, y: 0.038567513, z: 0.3272725, w: 0.93349546}
  m_LocalPosition: {x: -0.005909313, y: 0.082680196, z: -0.030114096}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1777047158}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1777047157
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1777047158}
  m_Layer: 0
  m_Name: pinky_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1777047158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1777047157}
  serializedVersion: 2
  m_LocalRotation: {x: 0.006781398, y: 0.024002517, z: 0.31257668, w: 0.949565}
  m_LocalPosition: {x: 0.0000003366732, y: 0.033087667, z: -0.000000014747458}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 794541317}
  m_Father: {fileID: 1775810478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1817938614
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1817938615}
  m_Layer: 0
  m_Name: LeftLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1817938615
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817938614}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000017847581, y: 0.0000000099993835, z: -0.021634454,
    w: 0.99976593}
  m_LocalPosition: {x: -0.40704706, y: 0.0000000071525554, z: 0.000000019073477}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1659492429}
  - {fileID: 1022431443}
  m_Father: {fileID: 435474585}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1823403079
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1823403080}
  m_Layer: 0
  m_Name: middle_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1823403080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1823403079}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0057915617, y: 0.02177615, z: 0.33323914, w: 0.942573}
  m_LocalPosition: {x: 0.00003727654, y: 0.029057303, z: 0.000018892706}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 510648140}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1823914912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1823914913}
  m_Layer: 0
  m_Name: index_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1823914913
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1823914912}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01261167, y: 0.016738364, z: 0.31796965, w: -0.9478693}
  m_LocalPosition: {x: 0.00045138545, y: 0.028107531, z: -0.0007394696}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1030914214}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1862972379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1862972380}
  m_Layer: 0
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1862972380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1862972379}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0060504675, y: 0.25895494, z: 0.96563125, w: 0.021496192}
  m_LocalPosition: {x: 0.0974114, y: -0.021429222, z: -0.0022249052}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1348073460}
  - {fileID: 1427020990}
  m_Father: {fileID: 788567744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1863590578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1863590579}
  - component: {fileID: 1863590580}
  m_Layer: 9
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1863590579
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1863590578}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86622465, y: 0.018038174, z: -0.010375827, w: 0.49922118}
  m_LocalPosition: {x: 0.0030536088, y: 0.22314286, z: 0.11494481}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7342576458949274615}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1863590580
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1863590578}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.12159393, y: 0.32100797, z: 0.12159393}
  m_Center: {x: 0.0032451823, y: 0.17523569, z: -0.045210827}
--- !u!1 &1863733098
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1863733099}
  m_Layer: 0
  m_Name: LeftHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1863733099
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1863733098}
  serializedVersion: 2
  m_LocalRotation: {x: 0.027987713, y: -0.09606037, z: 0.021799589, w: 0.9947431}
  m_LocalPosition: {x: -0.0900276, y: 0.014419771, z: -0.014306784}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1492066494}
  m_Father: {fileID: 651974929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1872868589
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1872868590}
  m_Layer: 0
  m_Name: RightHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1872868590
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1872868589}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.016574945, w: 0.9998627}
  m_LocalPosition: {x: 0.039453283, y: 0.0000018821366, z: -0.000000646774}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 433483583}
  m_Father: {fileID: 614345930}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1891208915
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1891208916}
  m_Layer: 0
  m_Name: Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1891208916
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1891208915}
  serializedVersion: 2
  m_LocalRotation: {x: 0.21772775, y: -0.000000054086108, z: 0.000000021804336, w: 0.97600955}
  m_LocalPosition: {x: 0.00000011290042, y: 0.24456844, z: 0.0013742375}
  m_LocalScale: {x: 1.0000012, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 236590512}
  m_Father: {fileID: 1569415569}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1891256919
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1891256920}
  m_Layer: 0
  m_Name: LeftHandPinky3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1891256920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1891256919}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.23479164, w: 0.9720457}
  m_LocalPosition: {x: -0.017240442, y: -2.8421696e-16, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1366389261}
  m_Father: {fileID: 764028470}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1899014273
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443361}
  - component: {fileID: 1899443362}
  - component: {fileID: 1771705686}
  - component: {fileID: 1771705685}
  m_Layer: 9
  m_Name: Hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443363}
  - component: {fileID: 1899443367}
  - component: {fileID: 1899443366}
  - component: {fileID: 1899443364}
  m_Layer: 9
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014277
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443365}
  - component: {fileID: 1899443368}
  m_Layer: 9
  m_Name: PuppetMaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014281
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443369}
  - component: {fileID: 1899443370}
  - component: {fileID: 1899443373}
  - component: {fileID: 1899443372}
  m_Layer: 9
  m_Name: LeftForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014283
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443371}
  - component: {fileID: 1899443379}
  - component: {fileID: 1899443374}
  m_Layer: 9
  m_Name: LeftFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014287
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443375}
  - component: {fileID: 1899443376}
  - component: {fileID: 1899443378}
  - component: {fileID: 1899443377}
  m_Layer: 9
  m_Name: LeftArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014293
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443381}
  - component: {fileID: 1899443384}
  - component: {fileID: 1899443383}
  - component: {fileID: 1899443382}
  m_Layer: 9
  m_Name: LeftHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443433}
  - component: {fileID: 1899443434}
  - component: {fileID: 1899443436}
  - component: {fileID: 1899443435}
  m_Layer: 9
  m_Name: RightArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014351
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443439}
  - component: {fileID: 1899443440}
  - component: {fileID: 1899443442}
  - component: {fileID: 1899443441}
  m_Layer: 9
  m_Name: LeftUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014355
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443443}
  - component: {fileID: 1899443444}
  - component: {fileID: 1899443447}
  - component: {fileID: 1899443446}
  m_Layer: 9
  m_Name: RightForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014357
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443445}
  - component: {fileID: 1899443449}
  - component: {fileID: 1899443448}
  m_Layer: 9
  m_Name: RightFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014367
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443455}
  - component: {fileID: 1899443458}
  - component: {fileID: 1899443457}
  - component: {fileID: 1899443456}
  m_Layer: 9
  m_Name: RightHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014393
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443353}
  - component: {fileID: 1899443354}
  - component: {fileID: 1899443356}
  - component: {fileID: 1899443355}
  m_Layer: 9
  m_Name: LeftLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443491}
  - component: {fileID: 1899443492}
  - component: {fileID: 1899443494}
  - component: {fileID: 1899443493}
  m_Layer: 9
  m_Name: RightLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443497}
  - component: {fileID: 1899443498}
  - component: {fileID: 1899443500}
  - component: {fileID: 1899443499}
  m_Layer: 9
  m_Name: RightUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1899014417
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899443505}
  - component: {fileID: 1899443506}
  - component: {fileID: 1899443508}
  - component: {fileID: 1899443507}
  m_Layer: 9
  m_Name: Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1899443353
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014393}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000014901159, y: -0, z: -0.021634473, w: 0.999766}
  m_LocalPosition: {x: -0.40704727, y: -0.000000029802326, z: 0.00000023469329}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443371}
  m_Father: {fileID: 1899443439}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443354
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014393}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1899443355
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014393}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443440}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: -1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.4793084
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.52069
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443356
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014393}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.082427025
  m_Height: 0.47596946
  m_Direction: 0
  m_Center: {x: -0.21635011, y: -0.00000028382695, z: -0.00000027655182}
--- !u!4 &1899443361
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014273}
  serializedVersion: 2
  m_LocalRotation: {x: -1.9106857e-15, y: -0.000000043711392, z: -0.00000004371139,
    w: 1}
  m_LocalPosition: {x: 0, y: 0.92342365, z: 0}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443439}
  - {fileID: 1899443497}
  - {fileID: 1899443505}
  m_Father: {fileID: 1899443365}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443362
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014273}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!4 &1899443363
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014275}
  serializedVersion: 2
  m_LocalRotation: {x: 0.033481907, y: -0.0000007076063, z: -0.00000013864538, w: 0.99943936}
  m_LocalPosition: {x: -0.000000057403916, y: 0.34064025, z: 0.046482116}
  m_LocalScale: {x: 1.0000012, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1899443505}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &1899443364
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014275}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443506}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &1899443365
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014277}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443361}
  m_Father: {fileID: 740791348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &1899443366
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014275}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.16272044
  m_Height: 0.2983208
  m_Direction: 1
  m_Center: {x: -0.00000026870148, y: 0.052053668, z: -0.027807938}
--- !u!54 &1899443367
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014275}
  serializedVersion: 5
  m_Mass: 5.4900002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &1899443368
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c86ba130e5a5458a98e3b482192a6dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  humanoidConfig: {fileID: 0}
  targetRoot: {fileID: 1023502522}
  state: 0
  stateSettings:
    killDuration: 1
    deadMuscleWeight: 0.01
    deadMuscleDamper: 2
    maxFreezeSqrVelocity: 0.02
    freezePermanently: 0
    enableAngularLimitsOnKill: 1
    enableInternalCollisionsOnKill: 1
  mode: 0
  blendTime: 0.1
  fixTargetTransforms: 1
  solverIterationCount: 6
  visualizeTargetPose: 1
  mappingWeight: 1
  pinWeight: 1
  muscleWeight: 1
  muscleSpring: 100
  muscleDamper: 0
  pinPow: 4
  pinDistanceFalloff: 5
  angularPinning: 0
  updateJointAnchors: 1
  supportTranslationAnimation: 0
  angularLimits: 0
  internalCollisions: 0
  muscles:
  - name: Hips
    joint: {fileID: 1771705685}
    target: {fileID: 519551633}
    props:
      group: 0
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 0
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: LeftUpLeg
    joint: {fileID: 1899443441}
    target: {fileID: 435474585}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 1
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: LeftLeg
    joint: {fileID: 1899443355}
    target: {fileID: 1817938615}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 2
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: LeftFoot
    joint: {fileID: 1899443374}
    target: {fileID: 1659492429}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 3
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: RightUpLeg
    joint: {fileID: 1899443499}
    target: {fileID: 1291548215}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 4
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: RightLeg
    joint: {fileID: 1899443493}
    target: {fileID: 311320240}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 5
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: RightFoot
    joint: {fileID: 1899443448}
    target: {fileID: 1537216688}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 6
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Spine1
    joint: {fileID: 1899443507}
    target: {fileID: 1569415569}
    props:
      group: 1
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 7
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: LeftArm
    joint: {fileID: 1899443377}
    target: {fileID: 2041163931}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 8
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: LeftForeArm
    joint: {fileID: 1899443372}
    target: {fileID: 943684963}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 9
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: LeftHand
    joint: {fileID: 1899443382}
    target: {fileID: 651974929}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 10
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Head
    joint: {fileID: 1899443364}
    target: {fileID: 236590512}
    props:
      group: 2
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 11
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: RightArm
    joint: {fileID: 1899443435}
    target: {fileID: 1204222773}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 12
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: RightForeArm
    joint: {fileID: 1899443446}
    target: {fileID: 823157133}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 13
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: RightHand
    joint: {fileID: 1899443456}
    target: {fileID: 1911527513}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 14
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  propMuscles: []
  solvers: []
  mapDisconnectedMuscles: 1
  storeTargetMappedState: 1
--- !u!4 &1899443369
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014281}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0025202783, y: 0.364013, z: -0.01183734, w: 0.9313152}
  m_LocalPosition: {x: -0.2710414, y: 0.000000007450578, z: -0.000000020954753}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443381}
  m_Father: {fileID: 1899443375}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443370
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014281}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!4 &1899443371
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014283}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0023852196, y: 0.048043612, z: 0.5650304, w: 0.8236667}
  m_LocalPosition: {x: -0.43269983, y: -0.00000004663162, z: -0.000000048707754}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 756529700}
  m_Father: {fileID: 1899443353}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &1899443372
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014281}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443376}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -1, z: -0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: -1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -42.717472
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 97.28253
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443373
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014281}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07318114
  m_Height: 0.25831234
  m_Direction: 0
  m_Center: {x: -0.11741432, y: -0.00000023338362, z: -0.00000016763802}
--- !u!153 &1899443374
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014283}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443354}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &1899443375
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014287}
  serializedVersion: 2
  m_LocalRotation: {x: -0.012020521, y: -0.07240768, z: 0.37568325, w: 0.92383707}
  m_LocalPosition: {x: -0.14517792, y: 0.1776429, z: -0.020192578}
  m_LocalScale: {x: 1.0000005, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443369}
  m_Father: {fileID: 1899443505}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443376
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014287}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1899443377
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014287}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443506}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -1, z: -0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: -1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443378
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014287}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.0813124
  m_Height: 0.29814544
  m_Direction: 0
  m_Center: {x: -0.13552083, y: 0.0000001601875, z: -0.000000022584576}
--- !u!54 &1899443379
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014283}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!4 &1899443381
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014293}
  serializedVersion: 2
  m_LocalRotation: {x: -0.05855469, y: 0.005765164, z: 0.018105028, w: 0.9981034}
  m_LocalPosition: {x: -0.23482943, y: 0.00000004901085, z: -0.00000010058282}
  m_LocalScale: {x: 0.9999999, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1899443369}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &1899443382
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014293}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443370}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -1, z: -0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: -1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443383
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014293}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.06604582
  m_Height: 0.1937344
  m_Direction: 0
  m_Center: {x: -0.08806079, y: -0.000000015708792, z: -0.0000002656306}
--- !u!54 &1899443384
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014293}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!4 &1899443433
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014345}
  serializedVersion: 2
  m_LocalRotation: {x: 0.923837, y: -0.37568322, z: -0.07240773, w: 0.012020599}
  m_LocalPosition: {x: 0.14517786, y: 0.1776447, z: -0.020192288}
  m_LocalScale: {x: 1.0000006, y: 1.0000005, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443443}
  m_Father: {fileID: 1899443505}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443434
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014345}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1899443435
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014345}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443506}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -1, z: -0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443436
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014345}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08131318
  m_Height: 0.2981483
  m_Direction: 0
  m_Center: {x: 0.13552196, y: 0.0000012088568, z: 0.00000036822172}
--- !u!4 &1899443439
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014351}
  serializedVersion: 2
  m_LocalRotation: {x: -0.46685073, y: -0.5240647, z: 0.50521725, w: 0.5021576}
  m_LocalPosition: {x: -0.099243164, y: -0.000000008676114, z: 0.000000008676114}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443353}
  m_Father: {fileID: 1899443361}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443440
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014351}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1899443441
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014351}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443362}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: -1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443442
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014351}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09158558
  m_Height: 0.4477517
  m_Direction: 0
  m_Center: {x: -0.20352362, y: -0.000000029802326, z: 0.00000013783574}
--- !u!4 &1899443443
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014355}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0025202779, y: 0.364013, z: -0.011837332, w: 0.93131524}
  m_LocalPosition: {x: 0.27104396, y: 0.000002503394, z: 0.0000005327164}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443455}
  m_Father: {fileID: 1899443433}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443444
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014355}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!4 &1899443445
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014357}
  serializedVersion: 2
  m_LocalRotation: {x: 0.13248642, y: 0.12472641, z: 0.5498335, w: 0.81521404}
  m_LocalPosition: {x: 0.43269977, y: 0.00000005962102, z: 0.00000034583817}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 503412404}
  m_Father: {fileID: 1899443491}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &1899443446
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014355}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443434}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -1, z: -0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -42.718376
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 97.281624
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443447
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014355}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07318184
  m_Height: 0.25831017
  m_Direction: 0
  m_Center: {x: 0.11741361, y: -0.000002166169, z: -0.0000017937268}
--- !u!153 &1899443448
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014357}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443492}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!54 &1899443449
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014357}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!4 &1899443455
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014367}
  serializedVersion: 2
  m_LocalRotation: {x: -0.05855471, y: 0.005765164, z: 0.01810505, w: 0.9981034}
  m_LocalPosition: {x: 0.23482749, y: -0.0000047476497, z: -0.0000036507836}
  m_LocalScale: {x: 0.9999999, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1899443443}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &1899443456
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014367}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443444}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -1, z: -0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443457
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014367}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.066045254
  m_Height: 0.19373274
  m_Direction: 0
  m_Center: {x: 0.088060625, y: -0.00000012207602, z: 0.0000000966829}
--- !u!54 &1899443458
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014367}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!4 &1899443491
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014403}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000074505806, y: 0.000000014901161, z: -0.02163446, w: 0.99976593}
  m_LocalPosition: {x: 0.40704727, y: 0.0000001192093, z: 0.000000067055225}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443445}
  m_Father: {fileID: 1899443497}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443492
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014403}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1899443493
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014403}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443498}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.4793084
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.52069
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443494
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014403}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.082427084
  m_Height: 0.4759698
  m_Direction: 0
  m_Center: {x: 0.2163499, y: 0.00000035547149, z: -0.0000000318777}
--- !u!4 &1899443497
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014409}
  serializedVersion: 2
  m_LocalRotation: {x: -0.50215745, y: 0.50521725, z: 0.5240648, w: -0.46685067}
  m_LocalPosition: {x: 0.09924364, y: 0.0000003066994, z: -0.000000008676156}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443491}
  m_Father: {fileID: 1899443361}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443498
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014409}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1899443499
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014409}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443362}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1899443500
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014409}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09158564
  m_Height: 0.447752
  m_Direction: 0
  m_Center: {x: 0.20352362, y: 0.00000005960465, z: -0.00000020489097}
--- !u!4 &1899443505
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014417}
  serializedVersion: 2
  m_LocalRotation: {x: -0.08568609, y: 0.000000013834777, z: -0.000000037587903, w: 0.99632215}
  m_LocalPosition: {x: -0.000000025769602, y: 0.28904787, z: -0.005722046}
  m_LocalScale: {x: 0.9999997, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1899443375}
  - {fileID: 1899443363}
  - {fileID: 1899443433}
  m_Father: {fileID: 1899443361}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1899443506
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014417}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1899443507
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014417}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1899443362}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!65 &1899443508
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899014417}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.2903557, y: 0.2854226, z: 0.17421342}
  m_Center: {x: -0.00000006587141, y: 0.12957105, z: 0.0065724384}
--- !u!1 &1911527512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1911527513}
  m_Layer: 0
  m_Name: RightHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1911527513
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1911527512}
  serializedVersion: 2
  m_LocalRotation: {x: -0.05855471, y: 0.005765164, z: 0.01810505, w: 0.9981034}
  m_LocalPosition: {x: 0.234827, y: -0.0000044285603, z: -0.0000036005529}
  m_LocalScale: {x: 0.9999999, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2048931244}
  - {fileID: 1525825665}
  - {fileID: 234521622}
  - {fileID: 712229567}
  - {fileID: 1479432771}
  - {fileID: 872981094}
  - {fileID: 1110567227}
  m_Father: {fileID: 823157133}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1926856645
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1926856646}
  m_Layer: 0
  m_Name: CC_Base_Tongue01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1926856646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1926856645}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029571867, y: 0.006775168, z: -0.11056743, w: 0.9938455}
  m_LocalPosition: {x: -0.02315749, y: 0.011044769, z: -0.00015940386}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1347422854}
  m_Father: {fileID: 402353773}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1949235395
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1949235397}
  - component: {fileID: 1949235396}
  m_Layer: 0
  m_Name: DebugLogManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1949235396
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949235395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1cbb37e6a1ce5e24f8a5180c05b1e779, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _settings: {fileID: 11400000, guid: d1b9c644629aebc499b9107aeb5c6ce1, type: 2}
--- !u!4 &1949235397
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949235395}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1950600340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1950600341}
  m_Layer: 21
  m_Name: Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1950600341
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1950600340}
  serializedVersion: 2
  m_LocalRotation: {x: 0.77128386, y: -0.026550846, z: 0.0323993, w: -0.6351116}
  m_LocalPosition: {x: -0.13594225, y: -0.02086684, z: 0.004009595}
  m_LocalScale: {x: 1.010579, y: 1.0150763, z: 0.9747495}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1074454516}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1985717119 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 8740357781843139938}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1db305b54b5c3934dbcbddbda5d14f5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2002885917
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2002885918}
  m_Layer: 0
  m_Name: RightForeArmRoll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2002885918
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2002885917}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000018626448, y: 2.775557e-16, z: 0.000000014901161, w: 1}
  m_LocalPosition: {x: 0.14892131, y: -0.00000034274672, z: -0.00000030333592}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 823157133}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2032455263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2032455264}
  m_Layer: 21
  m_Name: AimTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2032455264
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2032455263}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4837934, y: 0.51108474, z: -0.4920204, w: 0.51249623}
  m_LocalPosition: {x: -0.025851786, y: 0.00839515, z: 0.0016169167}
  m_LocalScale: {x: 1.0152817, y: 1.0099486, z: 0.9751789}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 908534942}
  m_Father: {fileID: 1110567227}
  m_LocalEulerAnglesHint: {x: -108.337, y: -265.796, z: 272.1}
--- !u!1 &2036937281
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2036937282}
  m_Layer: 0
  m_Name: spine_03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2036937282
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2036937281}
  serializedVersion: 2
  m_LocalRotation: {x: -0.15933202, y: -0.00021612641, z: -0.0018017887, w: 0.9872234}
  m_LocalPosition: {x: -7.858033e-10, y: 0.12466317, z: 0.0000017378482}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 391831348}
  - {fileID: 2132946929}
  - {fileID: 2084455373}
  m_Father: {fileID: 791390003}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2037831839
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2037831840}
  m_Layer: 0
  m_Name: ring_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2037831840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2037831839}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10743107, y: 0.03920835, z: 0.32272127, w: -0.93955964}
  m_LocalPosition: {x: 0.0020114228, y: 0.08760652, z: -0.015832042}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1331996526}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2041163930
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2041163931}
  m_Layer: 0
  m_Name: LeftArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2041163931
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2041163930}
  serializedVersion: 2
  m_LocalRotation: {x: 0.016532756, y: 0.025041873, z: 0.32079402, w: 0.9466735}
  m_LocalPosition: {x: -0.112815596, y: 0.00000015258789, z: -0.000000028610229}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1031367421}
  - {fileID: 943684963}
  m_Father: {fileID: 513596237}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2048931243
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2048931244}
  m_Layer: 0
  m_Name: RightHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2048931244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2048931243}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0016747925, y: 0.13099872, z: 0.09642225, w: 0.986681}
  m_LocalPosition: {x: 0.091051, y: -0.013265024, z: -0.022518286}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 849249593}
  m_Father: {fileID: 1911527513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2059762509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2059762510}
  - component: {fileID: 2059762511}
  m_Layer: 0
  m_Name: Sci_Fi_BGraph
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2059762510
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2059762509}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &2059762511
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2059762509}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 75b47edf762049149a922ff6504fef34, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7392520633234987534, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 2036937282}
  - {fileID: 1729486767}
  - {fileID: 2132946929}
  - {fileID: 652127906}
  - {fileID: 391831348}
  - {fileID: 922988970}
  - {fileID: 2084455373}
  - {fileID: 791390003}
  - {fileID: 818439002}
  - {fileID: 490280331}
  - {fileID: 1639236270}
  - {fileID: 1107498345}
  - {fileID: 221890809}
  - {fileID: 1313544555}
  - {fileID: 393862047}
  - {fileID: 1143128288}
  - {fileID: 821742122}
  - {fileID: 365752386}
  - {fileID: 788567744}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 788567744}
  m_AABB:
    m_Center: {x: 0.0010021627, y: 0.30867955, z: 0.021864533}
    m_Extent: {x: 0.6461469, y: 0.24538127, z: 0.20483333}
  m_DirtyAABB: 0
--- !u!1 &2060465283
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2060465284}
  m_Layer: 0
  m_Name: Female1 (1) Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2060465284
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2060465283}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1659082872}
  - {fileID: 2718649208880983646}
  - {fileID: 1603960884}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2060988119
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2060988120}
  - component: {fileID: 2060988122}
  - component: {fileID: 2060988121}
  m_Layer: 21
  m_Name: Laser2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2060988120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2060988119}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000007, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 908534942}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!23 &2060988121
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2060988119}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2060988122
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2060988119}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2062405670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2062405671}
  m_Layer: 21
  m_Name: Left Hand Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2062405671
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2062405670}
  serializedVersion: 2
  m_LocalRotation: {x: 0.25672767, y: -0.010788244, z: 0.79459333, w: -0.5500874}
  m_LocalPosition: {x: 0.028688993, y: -0.09583068, z: -0.004698406}
  m_LocalScale: {x: 0.9793076, y: 1.0065103, z: 1.0144148}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1074454516}
  m_LocalEulerAnglesHint: {x: 55.4101, y: -238.4923, z: -34.3354}
--- !u!1 &2071019249
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2071019250}
  m_Layer: 0
  m_Name: pinky_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2071019250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2071019249}
  serializedVersion: 2
  m_LocalRotation: {x: 0.14113404, y: 0.041423872, z: 0.3272475, w: -0.93342084}
  m_LocalPosition: {x: 0.0059110494, y: 0.08264578, z: -0.030138062}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 289717372}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2082184159
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2082184160}
  m_Layer: 0
  m_Name: RightHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2082184160
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2082184159}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000002611886, y: -0.000000045327027, z: 0.05622665, w: 0.99841803}
  m_LocalPosition: {x: 0.02505461, y: -0.000002072149, z: -0.0000027764395}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 479941565}
  m_Father: {fileID: 66172616}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2084455372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2084455373}
  m_Layer: 0
  m_Name: neck_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2084455373
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2084455372}
  serializedVersion: 2
  m_LocalRotation: {x: 0.2695284, y: -0.0010341108, z: 0.007674157, w: 0.9629614}
  m_LocalPosition: {x: 0.000000007683412, y: 0.26940826, z: -0.000002078712}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2117149252}
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2087275941
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2087275942}
  m_Layer: 0
  m_Name: RightHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2087275942
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2087275941}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000015156895, y: -0.000000047614854, z: 0.23595853, w: 0.97176313}
  m_LocalPosition: {x: 0.020537997, y: 0.000003252391, z: 0.0000028518007}
  m_LocalScale: {x: 0.9999995, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 512595014}
  m_Father: {fileID: 692375174}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2095647994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2095647995}
  m_Layer: 0
  m_Name: index_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2095647995
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2095647994}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0051638926, y: 0.04095576, z: 0.32314914, w: 0.9454473}
  m_LocalPosition: {x: -0.001621192, y: 0.09508102, z: 0.017892344}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 479989158}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2115578592
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2115578593}
  m_Layer: 0
  m_Name: LeftHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2115578593
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115578592}
  serializedVersion: 2
  m_LocalRotation: {x: 0.80003554, y: 0.1228482, z: 0.07422334, w: 0.5825311}
  m_LocalPosition: {x: -0.011838579, y: -0.0088583585, z: 0.021988293}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1114303337}
  m_Father: {fileID: 651974929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2117149251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2117149252}
  m_Layer: 0
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2117149252
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2117149251}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14982721, y: 0.0007031407, z: -0.0061363867, w: 0.9886929}
  m_LocalPosition: {x: -0.00000002421439, y: 0.07173049, z: 0.000000044703473}
  m_LocalScale: {x: 1.0000001, y: 0.9999996, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1194285621}
  m_Father: {fileID: 2084455373}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2131469342
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2131469343}
  m_Layer: 0
  m_Name: CC_Base_R_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2131469343
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2131469342}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5000003, y: -0.49999973, z: -0.5000003, w: -0.49999976}
  m_LocalPosition: {x: 0.06211906, y: 0.058755912, z: -0.032109868}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2132946928
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2132946929}
  m_Layer: 0
  m_Name: clavicle_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2132946929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2132946928}
  serializedVersion: 2
  m_LocalRotation: {x: 0.009416448, y: 0.16666275, z: -0.6722113, w: 0.72129524}
  m_LocalPosition: {x: 0.047479425, y: 0.21393648, z: 0.002574429}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1729486767}
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &20886035627276422
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8507390164161050080}
  - component: {fileID: 8507390164161050082}
  - component: {fileID: 8507390164161050081}
  - component: {fileID: 8507390164161050083}
  m_Layer: 9
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &242333729163533167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5127213141868635989}
  - component: {fileID: 5127213141868635991}
  - component: {fileID: 5127213141868635990}
  - component: {fileID: 5127213141868635992}
  m_Layer: 9
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &391968514503198763
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  serializedVersion: 2
  m_LocalRotation: {x: -0.026243053, y: 0.026773466, z: 0.71181405, w: 0.7013668}
  m_LocalPosition: {x: -0.14536102, y: 0.33108318, z: -0.08794619}
  m_LocalScale: {x: 0.99999976, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4163107321374676012}
  m_Father: {fileID: 3672910278980562935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &739689946495207884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00025808235, y: 0.00028837987, z: 0.99977154, w: -0.021374444}
  m_LocalPosition: {x: -0.09741045, y: -0.021480039, z: -0.00220668}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5127213141868635989}
  m_Father: {fileID: 4334577748699632903}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &931668667661383315
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
  m_LocalPosition: {x: -0.00000004121102, y: 0.24876305, z: -0.00000003562698}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4163107321374676012}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1081494905603953626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4334577748699632903}
  - component: {fileID: 4334577748699632905}
  - component: {fileID: 4334577748699632904}
  - component: {fileID: 4334577748699632906}
  m_Layer: 9
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1161014949539902938
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  serializedVersion: 2
  m_LocalRotation: {x: -0.005145862, y: -0.00012410844, z: 0.02421719, w: 0.99969345}
  m_LocalPosition: {x: 0.00000020675361, y: 0.47928712, z: -0.00000013661337}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7342576458949274615}
  m_Father: {fileID: 1802066833543739433}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1372511727054042636
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4452510089258981396}
  - component: {fileID: 4452510089258981399}
  - component: {fileID: 4452510089258981398}
  - component: {fileID: 4452510089258981397}
  m_Layer: 9
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1802066833543739433
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000269411, y: 0.00016035745, z: 0.9997506, w: 0.022329211}
  m_LocalPosition: {x: 0.0974114, y: -0.021429218, z: -0.0022248626}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1161014949539902938}
  m_Father: {fileID: 4334577748699632903}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2057176480679072977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378520955643520241}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242954, y: 0.072389625, z: -0.036033686, w: 0.96155715}
  m_LocalPosition: {x: 0.00000044517208, y: 0.49370146, z: -0.00000059790904}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1038214041}
  m_Father: {fileID: 5127213141868635989}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2378520955643520241
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2057176480679072977}
  - component: {fileID: 2378520955643520242}
  - component: {fileID: 2378520955643520243}
  m_Layer: 9
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!54 &2378520955643520242
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378520955643520241}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &2378520955643520243
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378520955643520241}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5127213141868635991}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &2718649208880983646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3382561355986842852}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4334577748699632903}
  m_Father: {fileID: 2060465284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3366497561474141229
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1161014949539902938}
  - component: {fileID: 3366497561474141231}
  - component: {fileID: 3366497561474141230}
  - component: {fileID: 3366497561474141232}
  m_Layer: 9
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &3366497561474141230
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.097055644
  m_Height: 0.543139
  m_Direction: 1
  m_Center: {x: -0.00000021420419, y: 0.24688114, z: -0.00000020023438}
--- !u!54 &3366497561474141231
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &3366497561474141232
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3965297141383911436}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.837453
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.16255
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3382561355986842852
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2718649208880983646}
  - component: {fileID: 3382561355986842853}
  m_Layer: 9
  m_Name: PuppetMaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &3382561355986842853
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3382561355986842852}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c86ba130e5a5458a98e3b482192a6dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  humanoidConfig: {fileID: 0}
  targetRoot: {fileID: 1603960884}
  state: 0
  stateSettings:
    killDuration: 1
    deadMuscleWeight: 0.01
    deadMuscleDamper: 2
    maxFreezeSqrVelocity: 0.02
    freezePermanently: 0
    enableAngularLimitsOnKill: 1
    enableInternalCollisionsOnKill: 1
  mode: 0
  blendTime: 0.1
  fixTargetTransforms: 1
  solverIterationCount: 6
  visualizeTargetPose: 1
  mappingWeight: 1
  pinWeight: 1
  muscleWeight: 1
  muscleSpring: 100
  muscleDamper: 0
  pinPow: 4
  pinDistanceFalloff: 5
  angularPinning: 0
  updateJointAnchors: 1
  supportTranslationAnimation: 0
  angularLimits: 0
  internalCollisions: 0
  muscles:
  - name: pelvis
    joint: {fileID: 4334577748699632906}
    target: {fileID: 788567744}
    props:
      group: 0
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 0
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_l
    joint: {fileID: 3656840819856038290}
    target: {fileID: 526572840}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 1
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_l
    joint: {fileID: 5127213141868635992}
    target: {fileID: 1654331587}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 2
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_l
    joint: {fileID: 2378520955643520243}
    target: {fileID: 1065482879}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 3
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_r
    joint: {fileID: 3965297141383911437}
    target: {fileID: 1862972380}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 4
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_r
    joint: {fileID: 3366497561474141232}
    target: {fileID: 1348073460}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 5
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_r
    joint: {fileID: 7342576458949274617}
    target: {fileID: 500869698}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 6
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: spine_02
    joint: {fileID: 5016455502951505493}
    target: {fileID: 791390003}
    props:
      group: 1
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 7
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_l
    joint: {fileID: 6359360710549211178}
    target: {fileID: 922988970}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 8
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_l
    joint: {fileID: 4229797488933211704}
    target: {fileID: 1639236270}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 9
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_l
    joint: {fileID: 5931440703684339419}
    target: {fileID: 1107498345}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 10
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_r
    joint: {fileID: 8215425578896893514}
    target: {fileID: 1729486767}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 11
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_r
    joint: {fileID: 8507390164161050083}
    target: {fileID: 1313544555}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 12
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_r
    joint: {fileID: 9031080444131072388}
    target: {fileID: 393862047}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 13
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: head
    joint: {fileID: 4452510089258981397}
    target: {fileID: 2117149252}
    props:
      group: 2
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 14
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  propMuscles: []
  solvers: []
  mapDisconnectedMuscles: 1
  storeTargetMappedState: 1
--- !u!1 &3656840819856038287
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 739689946495207884}
  - component: {fileID: 3656840819856038289}
  - component: {fileID: 3656840819856038288}
  - component: {fileID: 3656840819856038290}
  m_Layer: 9
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &3656840819856038288
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.10784255
  m_Height: 0.52723026
  m_Direction: 1
  m_Center: {x: -0.0000002859161, y: 0.23964992, z: -0.00000034327965}
--- !u!54 &3656840819856038289
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &3656840819856038290
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4334577748699632905}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &3672910278980562935
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037494026, y: 0.00000367303, z: 0.00011810292, w: 0.9992969}
  m_LocalPosition: {x: 0.000027410932, y: 0.106513344, z: 0.030964514}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 391968514503198763}
  - {fileID: 8215425578896893511}
  - {fileID: 4452510089258981396}
  m_Father: {fileID: 4334577748699632903}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3965297141383911434
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1802066833543739433}
  - component: {fileID: 3965297141383911436}
  - component: {fileID: 3965297141383911435}
  - component: {fileID: 3965297141383911437}
  m_Layer: 9
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &3965297141383911435
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.107839614
  m_Height: 0.52721584
  m_Direction: 1
  m_Center: {x: 0.00000011175871, y: 0.23964334, z: -0.00000005801848}
--- !u!54 &3965297141383911436
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &3965297141383911437
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4334577748699632905}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &4163107321374676012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000048987545, y: -0.000012127677, z: -0.004907473, w: 0.99998796}
  m_LocalPosition: {x: -0.0000004894101, y: 0.28133073, z: -0.00000010879015}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 931668667661383315}
  m_Father: {fileID: 391968514503198763}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4229797488933211701
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4163107321374676012}
  - component: {fileID: 4229797488933211703}
  - component: {fileID: 4229797488933211702}
  - component: {fileID: 4229797488933211704}
  m_Layer: 9
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &4229797488933211702
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075959265
  m_Height: 0.2736391
  m_Direction: 1
  m_Center: {x: 0.0000005689216, y: 0.12438155, z: -0.00000009420073}
--- !u!54 &4229797488933211703
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &4229797488933211704
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6359360710549211177}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.5623221
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.43768
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &4334577748699632903
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000006556511, y: -0.0000038331455, z: -0.00001936654, w: 1}
  m_LocalPosition: {x: -0.0000029843027, y: 1.1449528, z: -0.03379741}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 739689946495207884}
  - {fileID: 1802066833543739433}
  - {fileID: 3672910278980562935}
  m_Father: {fileID: 2718649208880983646}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &4334577748699632904
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.23321414, y: 0.17533009, z: 0.13992849}
  m_Center: {x: 0.0000044474327, y: 0.028164964, z: 0.016374627}
--- !u!54 &4334577748699632905
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &4334577748699632906
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 0}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 2
  m_YMotion: 2
  m_ZMotion: 2
  m_AngularXMotion: 2
  m_AngularYMotion: 2
  m_AngularZMotion: 2
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &4452510089258981396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  serializedVersion: 2
  m_LocalRotation: {x: -0.037495185, y: -0.000004858333, z: -0.00009620549, w: 0.99929684}
  m_LocalPosition: {x: 0.00010460752, y: 0.45029494, z: -0.0686966}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3672910278980562935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &4452510089258981397
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5016455502951505491}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &4452510089258981398
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.14914049
  m_Height: 0.27342424
  m_Direction: 1
  m_Center: {x: 0.00015779176, y: 0.0654333, z: -0.014051051}
--- !u!54 &4452510089258981399
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  serializedVersion: 5
  m_Mass: 5.4900002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &5016455502951505490
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3672910278980562935}
  - component: {fileID: 5016455502951505491}
  - component: {fileID: 5016455502951505492}
  - component: {fileID: 5016455502951505493}
  m_Layer: 9
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!54 &5016455502951505491
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &5016455502951505492
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.29151765, y: 0.43816063, z: 0.17491059}
  m_Center: {x: 0.00012554858, y: 0.19527864, z: -0.039149012}
--- !u!153 &5016455502951505493
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4334577748699632905}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &5028939843267514978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8215425578896893511}
  - component: {fileID: 8215425578896893513}
  - component: {fileID: 8215425578896893512}
  - component: {fileID: 8215425578896893514}
  m_Layer: 9
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5127213141868635989
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00548152, y: 0.00012282522, z: -0.022256006, w: 0.99973726}
  m_LocalPosition: {x: -0.00000054202985, y: 0.4793005, z: -0.0000004945031}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2057176480679072977}
  m_Father: {fileID: 739689946495207884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &5127213141868635990
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09705828
  m_Height: 0.54307145
  m_Direction: 1
  m_Center: {x: 0.0000002207234, y: 0.24685049, z: -0.00000030291264}
--- !u!54 &5127213141868635991
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &5127213141868635992
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3656840819856038289}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.62689
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.37311
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &5931440703684339416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 931668667661383315}
  - component: {fileID: 5931440703684339418}
  - component: {fileID: 5931440703684339417}
  - component: {fileID: 5931440703684339419}
  m_Layer: 9
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &5931440703684339417
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.069964565
  m_Height: 0.20522939
  m_Direction: 1
  m_Center: {x: 0.00000040698797, y: 0.093286045, z: -0.000000031664964}
--- !u!54 &5931440703684339418
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &5931440703684339419
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4229797488933211703}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &6172228005745030767
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9031080444131072385}
  - component: {fileID: 9031080444131072387}
  - component: {fileID: 9031080444131072386}
  - component: {fileID: 9031080444131072388}
  m_Layer: 9
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6359360710549211175
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 391968514503198763}
  - component: {fileID: 6359360710549211177}
  - component: {fileID: 6359360710549211176}
  - component: {fileID: 6359360710549211178}
  m_Layer: 9
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &6359360710549211176
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08439919
  m_Height: 0.30946368
  m_Direction: 1
  m_Center: {x: 0.0000002495945, y: 0.14066541, z: -0.0000000111758744}
--- !u!54 &6359360710549211177
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &6359360710549211178
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5016455502951505491}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7212030796461448557
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7342576458949274615}
  - component: {fileID: 7342576458949274616}
  - component: {fileID: 7342576458949274617}
  m_Layer: 9
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7342576458949274615
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7212030796461448557}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213843, y: -0.07327937, z: 0.035943985, w: 0.9615725}
  m_LocalPosition: {x: -0.0000004246831, y: 0.49376267, z: -0.00000063888734}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1863590579}
  m_Father: {fileID: 1161014949539902938}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &7342576458949274616
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7212030796461448557}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &7342576458949274617
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7212030796461448557}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3366497561474141231}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &8215425578896893511
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02410228, y: -0.028923262, z: -0.7123166, w: 0.7008478}
  m_LocalPosition: {x: 0.14615616, y: 0.33055305, z: -0.08785249}
  m_LocalScale: {x: 0.99999976, y: 0.9999995, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8507390164161050080}
  m_Father: {fileID: 3672910278980562935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &8215425578896893512
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08442511
  m_Height: 0.30955872
  m_Direction: 1
  m_Center: {x: -0.00000006286429, y: 0.14070858, z: 0.00000039115562}
--- !u!54 &8215425578896893513
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &8215425578896893514
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5016455502951505491}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &8507390164161050080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000016186383, y: 0.000018548217, z: 0.007330766, w: 0.9999731}
  m_LocalPosition: {x: 0.0000007487835, y: 0.28141713, z: 0.0000009151411}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9031080444131072385}
  m_Father: {fileID: 8215425578896893511}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &8507390164161050081
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075982586
  m_Height: 0.27391738
  m_Direction: 1
  m_Center: {x: -0.00000032421664, y: 0.12450803, z: 0.0000000035406056}
--- !u!54 &8507390164161050082
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &8507390164161050083
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8215425578896893513}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.8402332
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.15976
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1001 &8740357781843139938
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"_modules.[0]","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[1]'
      value: '"path":"_modules.[1]","value":$eref:1'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[2]'
      value: '"path":"_modules.[2]","value":$eref:2'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[3]'
      value: '"path":"_modules.[3]","value":$eref:3'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[4]'
      value: '"path":"_modules.[4]","value":$eref:4'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[5]'
      value: '"path":"_modules.[5]","value":$eref:5'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[6]'
      value: '"path":"_modules.[6]","value":$eref:6'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[7]'
      value: '"path":"_modules.[7]","value":$eref:7'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 1374703566}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[1]'
      value: 
      objectReference: {fileID: 1985717119}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[2]'
      value: 
      objectReference: {fileID: 549187623}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[3]'
      value: 
      objectReference: {fileID: 1218358743}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[4]'
      value: 
      objectReference: {fileID: 1218358741}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[5]'
      value: 
      objectReference: {fileID: 877616362}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[6]'
      value: 
      objectReference: {fileID: 877616363}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[7]'
      value: 
      objectReference: {fileID: 1476540753}
    - target: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: DynamicLayeredCharacterAnimations
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -41.24336
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.67698
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5615533298249553943, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Name
      value: Female1New
      objectReference: {fileID: 0}
    - target: {fileID: 5615533298249553943, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5954690637389537017, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 5954690637389537017, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 6591187809807469422, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 6591187809807469422, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 6763110221404748807, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: _AnimationManager
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7421254585528994050, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7421254585528994050, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 8152044633288677640, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: layeredAnimationManager
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[0]'
      value: 
      objectReference: {fileID: 1751654721}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[1]'
      value: 
      objectReference: {fileID: 788567744}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[2]'
      value: 
      objectReference: {fileID: 365752386}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[3]'
      value: 
      objectReference: {fileID: 791390003}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[4]'
      value: 
      objectReference: {fileID: 2036937282}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[5]'
      value: 
      objectReference: {fileID: 818439002}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[6]'
      value: 
      objectReference: {fileID: 652127906}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[7]'
      value: 
      objectReference: {fileID: 391831348}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[8]'
      value: 
      objectReference: {fileID: 922988970}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[9]'
      value: 
      objectReference: {fileID: 1639236270}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[10]'
      value: 
      objectReference: {fileID: 1107498345}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[11]'
      value: 
      objectReference: {fileID: 2095647995}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[12]'
      value: 
      objectReference: {fileID: 479989158}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[13]'
      value: 
      objectReference: {fileID: 130749904}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[14]'
      value: 
      objectReference: {fileID: 729983694}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[15]'
      value: 
      objectReference: {fileID: 510648140}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[16]'
      value: 
      objectReference: {fileID: 1823403080}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[17]'
      value: 
      objectReference: {fileID: 1775810478}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[18]'
      value: 
      objectReference: {fileID: 1777047158}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[19]'
      value: 
      objectReference: {fileID: 794541317}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[20]'
      value: 
      objectReference: {fileID: 1708922341}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[21]'
      value: 
      objectReference: {fileID: 1657561067}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[22]'
      value: 
      objectReference: {fileID: 1681152083}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[23]'
      value: 
      objectReference: {fileID: 1770285347}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[24]'
      value: 
      objectReference: {fileID: 1486610823}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[25]'
      value: 
      objectReference: {fileID: 596950364}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[26]'
      value: 
      objectReference: {fileID: 490280331}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[27]'
      value: 
      objectReference: {fileID: 1143128288}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[28]'
      value: 
      objectReference: {fileID: 2132946929}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[29]'
      value: 
      objectReference: {fileID: 1729486767}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[30]'
      value: 
      objectReference: {fileID: 1313544555}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[31]'
      value: 
      objectReference: {fileID: 393862047}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[32]'
      value: 
      objectReference: {fileID: 493927424}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[33]'
      value: 
      objectReference: {fileID: 1030914214}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[34]'
      value: 
      objectReference: {fileID: 1823914913}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[35]'
      value: 
      objectReference: {fileID: 507680816}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[36]'
      value: 
      objectReference: {fileID: 709975675}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[37]'
      value: 
      objectReference: {fileID: 873247521}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[38]'
      value: 
      objectReference: {fileID: 2071019250}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[39]'
      value: 
      objectReference: {fileID: 289717372}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[40]'
      value: 
      objectReference: {fileID: 532689012}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[41]'
      value: 
      objectReference: {fileID: 2037831840}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[42]'
      value: 
      objectReference: {fileID: 1331996526}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[43]'
      value: 
      objectReference: {fileID: 123809142}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[44]'
      value: 
      objectReference: {fileID: 439328758}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[45]'
      value: 
      objectReference: {fileID: 129380941}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[46]'
      value: 
      objectReference: {fileID: 1253068708}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[47]'
      value: 
      objectReference: {fileID: 221890809}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[48]'
      value: 
      objectReference: {fileID: 821742122}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[49]'
      value: 
      objectReference: {fileID: 2084455373}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[50]'
      value: 
      objectReference: {fileID: 2117149252}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[51]'
      value: 
      objectReference: {fileID: 1194285621}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[52]'
      value: 
      objectReference: {fileID: 402353773}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[53]'
      value: 
      objectReference: {fileID: 596914987}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[54]'
      value: 
      objectReference: {fileID: 1926856646}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[55]'
      value: 
      objectReference: {fileID: 1347422854}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[56]'
      value: 
      objectReference: {fileID: 1699357223}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[57]'
      value: 
      objectReference: {fileID: 233545467}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[58]'
      value: 
      objectReference: {fileID: 2131469343}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[59]'
      value: 
      objectReference: {fileID: 221709058}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[60]'
      value: 
      objectReference: {fileID: 640277526}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[61]'
      value: 
      objectReference: {fileID: 526572840}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[62]'
      value: 
      objectReference: {fileID: 1654331587}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[63]'
      value: 
      objectReference: {fileID: 1285479708}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[64]'
      value: 
      objectReference: {fileID: 1065482879}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[65]'
      value: 
      objectReference: {fileID: 95360380}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[66]'
      value: 
      objectReference: {fileID: 659195098}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[67]'
      value: 
      objectReference: {fileID: 1862972380}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[68]'
      value: 
      objectReference: {fileID: 1348073460}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[69]'
      value: 
      objectReference: {fileID: 1007695412}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[70]'
      value: 
      objectReference: {fileID: 500869698}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[71]'
      value: 
      objectReference: {fileID: 1522317842}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'hierarchy.Array.data[72]'
      value: 
      objectReference: {fileID: 1427020990}
    - target: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 8202338679235432747, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 8830726950650311852, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 8830726950650311852, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 8950689789528778046, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 8950689789528778046, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 9195335255939506981, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 9195335255939506981, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 73df3382d6e5d1a4a9e6a91c8157ab90, type: 3}
--- !u!4 &9031080444131072385
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  serializedVersion: 2
  m_LocalRotation: {x: 0.102513105, y: -0.0019429321, z: 0.026160488, w: 0.9943857}
  m_LocalPosition: {x: 0.00000017648561, y: 0.24901599, z: 0.00000016253436}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8507390164161050080}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &9031080444131072386
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07003571
  m_Height: 0.20543809
  m_Direction: 1
  m_Center: {x: -0.0000005289913, y: 0.09338101, z: -0.0000000018626454}
--- !u!54 &9031080444131072387
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &9031080444131072388
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8507390164161050082}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 961739753}
  - {fileID: 203844589}
  - {fileID: 740791348}
  - {fileID: 236533411}
  - {fileID: 2060465284}
  - {fileID: 8740357781843139938}
  - {fileID: 1949235397}
