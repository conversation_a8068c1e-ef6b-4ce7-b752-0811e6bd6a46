// Designed by KINEMATION, 2025.
// Extension methods to integrate MagicBlend with Animancer

using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using YourProject.Animation;

namespace KINEMATION.MagicBlend.Runtime
{
    /// <summary>
    /// Extension methods to make MagicBlendAsset work seamlessly with Animancer
    /// </summary>
    public static class AnimancerMagicBlendExtensions
    {
        /// <summary>
        /// Plays a MagicBlendAsset using Animancer with the specified fade duration.
        /// This creates a MagicBlendTransition internally and plays it.
        /// </summary>
        /// <param name="animancer">The AnimancerComponent to play on</param>
        /// <param name="blendAsset">The MagicBlendAsset to play</param>
        /// <param name="fadeTime">The fade duration for the transition</param>
        /// <returns>The AnimancerState that was created and played</returns>
        public static AnimancerState Play(this AnimancerComponent animancer, MagicBlendAsset blendAsset, float fadeTime = 0.25f)
        {
            if (animancer == null)
            {
                UnityEngine.Debug.LogError("AnimancerComponent is null");
                return null;
            }

            if (blendAsset == null)
            {
                UnityEngine.Debug.LogError("MagicBlendAsset is null");
                return null;
            }

            var transition = new MagicBlendTransition(blendAsset, fadeTime);
            return animancer.Play(transition);
        }

        /// <summary>
        /// Plays a MagicBlendAsset using Animancer with the specified fade duration and mode.
        /// This creates a MagicBlendTransition internally and plays it.
        /// </summary>
        /// <param name="animancer">The AnimancerComponent to play on</param>
        /// <param name="blendAsset">The MagicBlendAsset to play</param>
        /// <param name="fadeTime">The fade duration for the transition</param>
        /// <param name="mode">The fade mode to use</param>
        /// <returns>The AnimancerState that was created and played</returns>
        public static AnimancerState Play(this AnimancerComponent animancer, MagicBlendAsset blendAsset, float fadeTime, FadeMode mode)
        {
            if (animancer == null)
            {
                UnityEngine.Debug.LogError("AnimancerComponent is null");
                return null;
            }

            if (blendAsset == null)
            {
                UnityEngine.Debug.LogError("MagicBlendAsset is null");
                return null;
            }

            var transition = new MagicBlendTransition(blendAsset, fadeTime);
            return animancer.Play(transition, fadeTime, mode);
        }

        /// <summary>
        /// Plays a MagicBlendAsset using AnimancerGraph with the specified fade duration.
        /// This creates a MagicBlendTransition internally and plays it.
        /// </summary>
        /// <param name="graph">The AnimancerGraph to play on</param>
        /// <param name="blendAsset">The MagicBlendAsset to play</param>
        /// <param name="fadeTime">The fade duration for the transition</param>
        /// <returns>The AnimancerState that was created and played</returns>
        public static AnimancerState Play(this AnimancerGraph graph, MagicBlendAsset blendAsset, float fadeTime = 0.25f)
        {
            if (graph == null)
            {
                UnityEngine.Debug.LogError("AnimancerGraph is null");
                return null;
            }

            if (blendAsset == null)
            {
                UnityEngine.Debug.LogError("MagicBlendAsset is null");
                return null;
            }

            var transition = new MagicBlendTransition(blendAsset, fadeTime);
            return graph.Layers[0].Play(transition);
        }

        /// <summary>
        /// Plays a MagicBlendAsset using AnimancerLayer with the specified fade duration.
        /// This creates a MagicBlendTransition internally and plays it.
        /// </summary>
        /// <param name="layer">The AnimancerLayer to play on</param>
        /// <param name="blendAsset">The MagicBlendAsset to play</param>
        /// <param name="fadeTime">The fade duration for the transition</param>
        /// <returns>The AnimancerState that was created and played</returns>
        public static AnimancerState Play(this AnimancerLayer layer, MagicBlendAsset blendAsset, float fadeTime = 0.25f)
        {
            if (layer == null)
            {
                UnityEngine.Debug.LogError("AnimancerLayer is null");
                return null;
            }

            if (blendAsset == null)
            {
                UnityEngine.Debug.LogError("MagicBlendAsset is null");
                return null;
            }

            var transition = new MagicBlendTransition(blendAsset, fadeTime);
            return layer.Play(transition);
        }

        /// <summary>
        /// Creates a MagicBlendTransition from a MagicBlendAsset.
        /// This allows for more advanced usage where you want to configure the transition before playing.
        /// </summary>
        /// <param name="blendAsset">The MagicBlendAsset to create a transition for</param>
        /// <param name="fadeTime">The fade duration for the transition</param>
        /// <returns>A new MagicBlendTransition</returns>
        public static MagicBlendTransition CreateTransition(this MagicBlendAsset blendAsset, float fadeTime = 0.25f)
        {
            if (blendAsset == null)
            {
                UnityEngine.Debug.LogError("MagicBlendAsset is null");
                return null;
            }

            return new MagicBlendTransition(blendAsset, fadeTime);
        }

        /// <summary>
        /// Gets the MagicBlendAnimancerState from an AnimancerState if it's a MagicBlend state.
        /// </summary>
        /// <param name="state">The AnimancerState to check</param>
        /// <returns>The MagicBlendAnimancerState if the state is a MagicBlend state, null otherwise</returns>
        public static MagicBlendAnimancerState AsMagicBlendState(this AnimancerState state)
        {
            return state as MagicBlendAnimancerState;
        }

        /// <summary>
        /// Checks if an AnimancerState is a MagicBlendAnimancerState.
        /// </summary>
        /// <param name="state">The AnimancerState to check</param>
        /// <returns>True if the state is a MagicBlendAnimancerState, false otherwise</returns>
        public static bool IsMagicBlendState(this AnimancerState state)
        {
            return state is MagicBlendAnimancerState;
        }
    }
}
