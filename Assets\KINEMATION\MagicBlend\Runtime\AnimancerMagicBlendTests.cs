using NUnit.Framework;
using UnityEngine;
using UnityEngine.Playables;
using NewAnimancer;
using YourProject.Animation; // Assuming this is the namespace for MagicBlendTransition/State
using KINEMATION.MagicBlend.Runtime;
using KINEMATION.KAnimationCore.Runtime.Rig; // For KRigComponent
using UnityEngine.Animations;
using Unity.Collections;
using System.Collections.Generic;

namespace Tests.EditMode
{
    public class AnimancerMagicBlendTests
    {
        private AnimancerComponent _animancer;
        private GameObject _gameObject;
        private MagicBlendAsset _mockAsset;
        private List<Object> _createdScriptableObjects; // To track and destroy SOs

        // Mock KRigComponent to control GetRigTransforms
        // Since GetRigTransforms() is not virtual, we'll use a wrapper approach
        public class MockKRigComponent : KRigComponent
        {
            public Transform[] MockTransforms = new Transform[0];

            // We can't override GetRigTransforms since it's not virtual
            // Instead, we'll manually populate the hierarchy field through reflection or setup
            public void SetMockTransforms(Transform[] transforms)
            {
                MockTransforms = transforms;
                // We need to set the internal hierarchy field
                // Since it's private, we'll use reflection or create a public method in tests
                var hierarchyField = typeof(KRigComponent).GetField("hierarchy",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (hierarchyField != null)
                {
                    var hierarchyList = new System.Collections.Generic.List<Transform>(transforms);
                    hierarchyField.SetValue(this, hierarchyList);
                }
            }
        }


        [SetUp]
        public void Setup()
        {
            _gameObject = new GameObject("TestAnimancerObject");
            var animator = _gameObject.AddComponent<Animator>();
            // Add our mockable KRigComponent
            _gameObject.AddComponent<MockKRigComponent>(); 

            _animancer = _gameObject.AddComponent<AnimancerComponent>();
            _animancer.Animator = animator;
            _animancer.InitializeGraph(); 

            _createdScriptableObjects = new List<Object>();
            _mockAsset = CreateMockMagicBlendAsset(); // Create a default one
        }

        [TearDown]
        public void Teardown()
        {
            if (_animancer != null && _animancer.Graph != null && _animancer.Graph.IsValidOrDispose())
            {
                _animancer.Graph.Destroy();
            }
            if (_gameObject != null)
            {
                Object.DestroyImmediate(_gameObject);
            }

            foreach (var so in _createdScriptableObjects)
            {
                if (so != null) Object.DestroyImmediate(so);
            }
            _createdScriptableObjects.Clear();
        }

        private MagicBlendAsset CreateMockMagicBlendAsset(string baseName = "TestBase", string overlayName = "TestOverlay", int rigBoneCount = 3)
        {
            var asset = ScriptableObject.CreateInstance<MagicBlendAsset>();
            _createdScriptableObjects.Add(asset); // Track for cleanup
            asset.name = "TestMagicBlendAsset";

            if (baseName != null)
            {
                asset.basePose = CreateMockAnimationClip(baseName, 1f, true);
            }
            if (overlayName != null)
            {
                asset.overlayPose = CreateMockAnimationClip(overlayName, 1f, false);
                asset.isAnimation = true; // Assume overlay is an animation for relevant tests
                asset.overlaySpeed = 1.5f;
            }
            
            asset.layeredBlends = new System.Collections.Generic.List<LayeredBlend>();
            asset.overrideOverlays = new System.Collections.Generic.List<OverrideOverlay>();
            asset.globalWeight = 1f; // Default global weight

            // KRigAsset setup (not directly used by state, KRigComponent on Animator is used)
            // asset.rigAsset = ScriptableObject.CreateInstance<KRigAsset>(); 
            // _createdScriptableObjects.Add(asset.rigAsset);
            // (No need to mock rigAsset if KRigComponent is mocked directly on the GO)

            // Setup mock transforms for the KRigComponent on the GameObject
            var mockKrig = _gameObject.GetComponent<MockKRigComponent>();
            if (mockKrig != null && rigBoneCount > 0)
            {
                var mockTransforms = new Transform[rigBoneCount];
                for (int i = 0; i < rigBoneCount; i++)
                {
                    var boneGO = new GameObject("MockBone" + i);
                    boneGO.transform.SetParent(_gameObject.transform);
                    mockTransforms[i] = boneGO.transform;
                }
                mockKrig.SetMockTransforms(mockTransforms);
            }
            
            return asset;
        }

        private AnimationClip CreateMockAnimationClip(string name, float length, bool isLooping)
        {
            // Direct creation of AnimationClip with settable length/looping is not straightforward in EditMode tests.
            // Animancer might handle null clips or default AnimationClips gracefully.
            // For tests that rely on specific length/looping, this mock might be insufficient.
            var clip = new AnimationClip { name = name };
            // Unity doesn't allow setting these directly on 'new AnimationClip()'
            // AnimationUtility.SetAnimationClipSettings can be used in Editor scripts, but not easily here.
            // We'll rely on the property accessors not throwing errors.
            if (clip != null) _createdScriptableObjects.Add(clip); // Technically not SO, but good to track if we could destroy
            return clip;
        }

        [Test]
        public void TestTransitionCreation()
        {
            var transition = new MagicBlendTransition(_mockAsset, 0.25f);
            Assert.AreEqual(_mockAsset, transition.Key, "Transition key should be the asset.");
            Assert.AreEqual(0.25f, transition.FadeDuration, "Transition fade duration mismatch.");
            Assert.AreEqual(FadeMode.FixedSpeed, transition.FadeMode, "Transition fade mode mismatch.");
        }

        [Test]
        public void TestTransitionCreateState()
        {
            var transition = new MagicBlendTransition(_mockAsset, 0.25f);
            var state = transition.CreateState();

            Assert.NotNull(state, "Created state should not be null.");
            Assert.IsInstanceOf<MagicBlendAnimancerState>(state, "Created state should be MagicBlendAnimancerState.");
            
            var magicBlendState = state as MagicBlendAnimancerState;
            Assert.NotNull(magicBlendState, "Cast to MagicBlendAnimancerState failed.");
            Assert.AreEqual(_mockAsset, magicBlendState.Asset, "State's asset mismatch.");
        }

        [Test]
        public void TestStateProperties()
        {
            // Note: Length and IsLooping from mock AnimationClips might not be reliable.
            // These assertions are included as per plan, but might need adjustment based on mock limitations.
            _mockAsset.basePose = CreateMockAnimationClip("SpecificBase", 2.5f, true); // Try to give specific values

            var state = new MagicBlendAnimancerState(_mockAsset);
            _animancer.Graph.Play(state); // Connect state to graph for some properties

            Assert.AreEqual(_mockAsset, state.MainObject, "State MainObject should be the asset.");
            Assert.AreEqual(_mockAsset, state.Asset, "State Asset should be the asset.");
            
            // These depend on how AnimationClip mock behaves or if Animancer bypasses it.
            // Default new AnimationClip() has length 0 or -1 depending on Unity version/context.
            // Assert.AreEqual(2.5f, state.Length, 0.01f, "State length mismatch."); 
            // Assert.IsTrue(state.IsLooping, "State IsLooping mismatch.");
            // For now, let's check they don't throw an error.
            Assert.DoesNotThrow(() => { var len = state.Length; });
            Assert.DoesNotThrow(() => { var loop = state.IsLooping; });
        }

        [Test]
        public void TestStateCreatePlayable_BasicStructure()
        {
            var state = new MagicBlendAnimancerState(_mockAsset);
            _animancer.Layers[0].Play(state); // This will call CreatePlayable internally

            Assert.IsTrue(state.Playable.IsValid(), "State playable should be valid.");
            Assert.AreEqual(2, state.Playable.GetInputCount(), "Job playable should have 2 inputs (base, overlay).");

            var scriptPlayable = (AnimationScriptPlayable)state.Playable;
            var jobData = scriptPlayable.GetJobData<MagicBlendJob>();

            Assert.IsTrue(jobData.boneHandles.IsCreated, "Job boneHandles should be created.");
            Assert.IsTrue(jobData.blendParams.IsCreated, "Job blendParams should be created.");

            var mockKrig = _gameObject.GetComponent<MockKRigComponent>();
            Assert.AreEqual(mockKrig.MockTransforms.Length, jobData.boneHandles.Length, "Bone handles length mismatch.");
        }

        [Test]
        public void TestStateCreatePlayable_WithOverrides()
        {
            _mockAsset.overrideOverlays.Add(new OverrideOverlay { overlay = CreateMockAnimationClip("Override1", 1f, false), weight = 1f });
            _mockAsset.overrideOverlays.Add(new OverrideOverlay { overlay = CreateMockAnimationClip("Override2", 1f, false), weight = 1f });

            var state = new MagicBlendAnimancerState(_mockAsset);
            _animancer.Layers[0].Play(state);

            Assert.IsTrue(state.Playable.IsValid(), "State playable is invalid.");
            var jobInputOverlayPlayable = state.Playable.GetInput(1); // Input 1 is overlay source
            Assert.IsTrue(jobInputOverlayPlayable.IsValid(), "Job's overlay input playable is invalid.");
            Assert.IsTrue(jobInputOverlayPlayable.IsPlayableOfType<AnimationLayerMixerPlayable>(), "Overlay input to job should be an AnimationLayerMixerPlayable.");

            var layerMixer = (AnimationLayerMixerPlayable)jobInputOverlayPlayable;
            Assert.AreEqual(_mockAsset.overrideOverlays.Count + 1, layerMixer.GetInputCount(), "Layer mixer input count mismatch.");
        }
        
        [Test]
        public void TestStateTimeAndSpeedPropagation()
        {
            var state = new MagicBlendAnimancerState(_mockAsset);
            _animancer.Layers[0].Play(state);

            state.Time = 0.5f;
            // RawTime getter in MagicBlendAnimancerState specifically gets time from _basePosePlayable.
            var basePosePlayable = state.Playable.GetInput(0); // Assuming base is input 0 to the job
            if (basePosePlayable.IsValid() && basePosePlayable.IsPlayableOfType<AnimationClipPlayable>())
            {
                 Assert.AreEqual(0.5f, basePosePlayable.GetTime(), 0.01f, "Time not propagated to basePosePlayable.");
            }
            else
            {
                // If basePosePlayable is not a simple AnimationClipPlayable (e.g. null), this check might change.
                // For this test, we assume a valid basePose was set up in _mockAsset.
                Assert.Inconclusive("Base pose playable not in expected state for time test.");
            }


            state.Speed = 2.0f;
            // AnimancerNode (base of AnimancerState) propagates Speed to its main Playable.
            Assert.AreEqual(2.0f, state.Playable.GetSpeed(), 0.01f, "Speed not propagated to main state playable.");
        }


        [Test]
        public void TestStateDestroy_DisposesNativeArrays()
        {
            Assume.That(_animancer.Graph.IsValid(), "Graph must be valid for this test.");

            var state = new MagicBlendAnimancerState(_mockAsset);
            _animancer.Layers[0].Play(state); // Creates playable and job data

            Assert.IsTrue(state.Playable.IsValid(), "Playable should be valid before destroy.");
            var scriptPlayable = (AnimationScriptPlayable)state.Playable;
            var jobData = scriptPlayable.GetJobData<MagicBlendJob>();

            // Store IsCreated status before destroying
            bool boneHandlesWereCreated = jobData.boneHandles.IsCreated;
            bool blendParamsWereCreated = jobData.blendParams.IsCreated;

            Assume.That(boneHandlesWereCreated, "Bone handles were not created by the state.");
            Assume.That(blendParamsWereCreated, "Blend params were not created by the state.");
            
            // Need to get a reference to the arrays themselves to check IsCreated after.
            // This is tricky as jobData is a struct copy.
            // A more direct test would be if MagicBlendAnimancerState exposed these arrays or a status.
            // For now, we will check if state.Destroy() can be called without error,
            // and then if we try to get job data again, it might fail or give uninitialized arrays.
            // This test highlights a difficulty in testing disposal of NativeArrays held by a job.
            
            // A pragmatic approach: if the job struct itself had an IsDisposed flag set by its owner state.
            // Or, we can rely on testing that Destroy doesn't throw and that re-accessing is problematic.

            _animancer.Graph.Stop(); // Stop state, which should lead to its destruction path
                                     // Or call state.Destroy() directly if Animancer's Stop() doesn't guarantee immediate full destruction
                                     // AnimancerState.OnStop calls Destroy if Index < 0 (i.e. not in a layer).
                                     // For a direct test:
            state.Destroy();


            // After Destroy, the NativeArrays inside the job *should* be disposed.
            // Accessing them via a *new* GetJobData call would give fresh (possibly uncreated) arrays
            // if the playable itself was destroyed and recreated, or an invalid playable.
            // If the playable is still valid but job data was disposed, GetJobData might throw.

            // Best we can do without internal state exposure:
            // Assert that the playable itself is no longer valid or the graph is destroyed.
            // Or, if we could get the job struct *by reference* (not possible for structs), we could check.
            // Let's assume that if state.Destroy() ran, it disposed them. This test is more of a
            // "does destroy run without error and cleans up what Animancer expects."
            Assert.Pass("State.Destroy() called. Assumed NativeArrays are disposed if state logic is correct. Manual verification or internal state access would be needed for a stronger check on specific NativeArray instances.");
            // To make this test more concrete, one would typically need to:
            // 1. Modify MagicBlendAnimancerState to allow access to the NativeArray instances (e.g., via properties for testing).
            // 2. Then, after state.Destroy(), check `testHandleArray.IsCreated == false`.
            // This is not possible with the current MagicBlendAnimancerState structure.
        }
    }
}
