// Quick integration test to verify MagicBlend + Animancer works
using UnityEngine;
using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;

namespace KINEMATION.MagicBlend.Runtime
{
    /// <summary>
    /// Simple test script to verify the MagicBlend + Animancer integration works correctly.
    /// Add this to a GameObject with Animator, AnimancerComponent, and KRigComponent.
    /// </summary>
    public class MagicBlendIntegrationTest : MonoBehaviour
    {
        [Header("Required Components")]
        [SerializeField] private AnimancerComponent _animancer;
        
        [Header("Test Assets")]
        [SerializeField] private MagicBlendAsset _testBlendAsset;
        
        [Header("Test Settings")]
        [SerializeField] private float _fadeTime = 0.25f;
        [SerializeField] private KeyCode _testKey = KeyCode.Space;

        private void Start()
        {
            // Auto-assign components if not set
            if (_animancer == null)
                _animancer = GetComponent<AnimancerComponent>();
                
            // Validate setup
            ValidateSetup();
        }

        private void Update()
        {
            if (Input.GetKeyDown(_testKey))
            {
                TestMagicBlendIntegration();
            }
        }

        /// <summary>
        /// Test the main integration feature
        /// </summary>
        public void TestMagicBlendIntegration()
        {
            if (_testBlendAsset == null)
            {
                Debug.LogError("Test blend asset is not assigned!", this);
                return;
            }

            try
            {
                // This is the main integration test - using the extension method
                var state = _animancer.Play(_testBlendAsset, _fadeTime);
                
                if (state != null)
                {
                    Debug.Log($"✅ SUCCESS: MagicBlend integration working! Playing: {_testBlendAsset.name}", this);
                    Debug.Log($"   - State Type: {state.GetType().Name}");
                    Debug.Log($"   - Length: {state.Length:F2}s");
                    Debug.Log($"   - Is MagicBlend: {state.IsMagicBlendState()}");
                    
                    if (state.IsMagicBlendState())
                    {
                        var magicBlendState = state.AsMagicBlendState();
                        Debug.Log($"   - Asset: {magicBlendState.Asset.name}");
                    }
                }
                else
                {
                    Debug.LogError("❌ FAILED: Play() returned null state", this);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ FAILED: Exception during integration test: {e.Message}", this);
                Debug.LogException(e, this);
            }
        }

        /// <summary>
        /// Test runtime modification features
        /// </summary>
        public void TestRuntimeModification()
        {
            if (_testBlendAsset == null) return;

            try
            {
                // Test runtime modification
                var originalWeight = _testBlendAsset.globalWeight;
                _testBlendAsset.SetGlobalWeight(0.5f);
                
                Debug.Log($"✅ Runtime modification test: Changed global weight from {originalWeight} to {_testBlendAsset.globalWeight}");
                
                // Restore original value
                _testBlendAsset.SetGlobalWeight(originalWeight);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Runtime modification test failed: {e.Message}", this);
            }
        }

        /// <summary>
        /// Validate that all required components are present
        /// </summary>
        private void ValidateSetup()
        {
            bool isValid = true;
            
            if (_animancer == null)
            {
                Debug.LogError("❌ AnimancerComponent is missing! Please add one to this GameObject.", this);
                isValid = false;
            }
            
            var animator = GetComponent<Animator>();
            if (animator == null)
            {
                Debug.LogError("❌ Animator component is missing! Please add one to this GameObject.", this);
                isValid = false;
            }
            
            var kRig = GetComponent<KINEMATION.KAnimationCore.Runtime.Rig.KRigComponent>();
            if (kRig == null)
            {
                Debug.LogError("❌ KRigComponent is missing! Please add one to this GameObject.", this);
                isValid = false;
            }
            
            if (_testBlendAsset == null)
            {
                Debug.LogWarning("⚠️ Test blend asset is not assigned. Please assign one to test the integration.", this);
            }
            
            if (isValid)
            {
                Debug.Log($"✅ Setup validation passed! Press {_testKey} to test MagicBlend integration.", this);
            }
        }

        /// <summary>
        /// Test creating transitions manually
        /// </summary>
        public void TestTransitionCreation()
        {
            if (_testBlendAsset == null) return;

            try
            {
                // Test transition creation
                var transition = _testBlendAsset.CreateTransition(_fadeTime);
                
                if (transition != null)
                {
                    Debug.Log($"✅ Transition creation test passed: {transition.GetType().Name}");
                    Debug.Log($"   - Key: {transition.Key}");
                    Debug.Log($"   - Fade Duration: {transition.FadeDuration}");
                    Debug.Log($"   - Fade Mode: {transition.FadeMode}");
                    
                    // Test playing the transition
                    var state = _animancer.Play(transition);
                    if (state != null)
                    {
                        Debug.Log($"✅ Transition play test passed: {state.GetType().Name}");
                    }
                }
                else
                {
                    Debug.LogError("❌ Transition creation returned null");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Transition creation test failed: {e.Message}", this);
            }
        }

        /// <summary>
        /// Run all tests
        /// </summary>
        [ContextMenu("Run All Tests")]
        public void RunAllTests()
        {
            Debug.Log("🧪 Running MagicBlend Integration Tests...", this);
            
            TestMagicBlendIntegration();
            TestRuntimeModification();
            TestTransitionCreation();
            
            Debug.Log("🧪 Integration tests completed!", this);
        }

        private void OnValidate()
        {
            // Auto-assign Animancer component if not set
            if (_animancer == null)
                _animancer = GetComponent<AnimancerComponent>();
        }
    }
}
