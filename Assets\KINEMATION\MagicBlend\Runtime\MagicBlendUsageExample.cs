﻿using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using NewAnimancer;
using UnityEngine;
using YourProject.Animation;

/// <summary>
/// Simple example showing how to use MagicBlend + Animancer integration
/// </summary>
public class MagicBlendUsageExample : MonoBehaviour
{
    [Header("Required Components")]
    [SerializeField] private HybridAnimancerComponent animancer;
    
    [<PERSON><PERSON>("MagicBlend Assets")]
    [SerializeField] private MagicBlendAsset idleBlend;
    [SerializeField] private MagicBlendAsset walkBlend;
    [SerializeField] private MagicBlendAsset runBlend;
    
    [Header("Optional: Transition Assets")]
    [SerializeField] private MagicBlendTransition attackTransition;
    
    [Header("Settings")]
    [SerializeField] private float fadeTime = 0.25f;

    private void Start()
    {
        // Start with idle animation
        if (idleBlend != null)
        {
            animancer.Play(idleBlend, fadeTime);
        }
    }

    private void Update()
    {
        // Example input handling
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            PlayIdle();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            PlayWalk();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            PlayRun();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha4))
        {
            PlayAttack();
        }
    }

    #region Animation Methods

    public void PlayIdle()
    {
        if (idleBlend != null)
        {
            var state = animancer.Play(idleBlend, fadeTime);
            Debug.Log($"Playing idle: {state}");
        }
    }

    public void PlayWalk()
    {
        if (walkBlend != null)
        {
            var state = animancer.Play(walkBlend, fadeTime);
            Debug.Log($"Playing walk: {state}");
        }
    }

    public void PlayRun()
    {
        if (runBlend != null)
        {
            // Use TryPlay to reuse existing state
            var state = animancer.TryPlay(runBlend, fadeTime);
            Debug.Log($"Playing run: {state}");
        }
    }

    public void PlayAttack()
    {
        if (attackTransition != null)
        {
            // Play on upper body layer for layered animation
            if (animancer.Layers.Count > 1)
            {
                var state = animancer.Layers[1].Play(attackTransition, 0.1f);
                Debug.Log($"Playing attack on layer 1: {state}");
            }
            else
            {
                var state = animancer.Play(attackTransition, 0.1f);
                Debug.Log($"Playing attack: {state}");
            }
        }
    }

    public void StopAllAnimations()
    {
        animancer.Stop();
    }

    public void StopSpecificAnimation(MagicBlendAsset asset)
    {
        if (asset != null)
        {
            var stoppedState = animancer.Stop(asset);
            Debug.Log($"Stopped: {stoppedState}");
        }
    }

    #endregion

    #region Advanced Usage Examples

    /// <summary>
    /// Example of getting a state without playing it immediately
    /// </summary>
    public void PrepareAnimation(MagicBlendAsset asset)
    {
        if (asset != null)
        {
            var state = animancer.GetOrCreate(asset);
            // State is created and registered but not playing
            // You can configure it before playing
            Debug.Log($"Prepared state: {state}");
        }
    }

    /// <summary>
    /// Example of conditional animation playing
    /// </summary>
    public void PlayAnimationIfNotCurrent(MagicBlendAsset asset)
    {
        if (asset != null)
        {
            // Check if this asset is already playing
            if (animancer.States.TryGet(asset, out var existingState))
            {
                if (!existingState.IsPlaying)
                {
                    animancer.Play(existingState, fadeTime);
                }
            }
            else
            {
                // Create and play new state
                animancer.Play(asset, fadeTime);
            }
        }
    }

    #endregion
}
