// Designed by KINEMATION, 2025.
// Example script showing how to use MagicBlendAsset with Animancer

using UnityEngine;
using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;

namespace KINEMATION.MagicBlend.Runtime
{
    /// <summary>
    /// Example script demonstrating how to use MagicBlendAsset with Animancer.
    /// This shows the integration working with the extension methods.
    /// </summary>
    public class MagicBlendExample : MonoBehaviour
    {
        [Header("Animancer Setup")]
        [SerializeField] private AnimancerComponent _animancer;
        
        [Header("MagicBlend Assets")]
        [SerializeField] private MagicBlendAsset _idleBlend;
        [SerializeField] private MagicBlendAsset _walkBlend;
        [SerializeField] private MagicBlendAsset _runBlend;
        
        [Header("Settings")]
        [SerializeField] private float _defaultFadeTime = 0.25f;
        [SerializeField] private KeyCode _idleKey = KeyCode.Alpha1;
        [SerializeField] private KeyCode _walkKey = KeyCode.Alpha2;
        [SerializeField] private KeyCode _runKey = KeyCode.Alpha3;

        private void Start()
        {
            // Ensure we have an Animancer component
            if (_animancer == null)
                _animancer = GetComponent<AnimancerComponent>();
                
            if (_animancer == null)
            {
                Debug.LogError("No AnimancerComponent found! Please add one to this GameObject.", this);
                return;
            }

            // Play the idle blend by default
            if (_idleBlend != null)
            {
                PlayIdleBlend();
            }
        }

        private void Update()
        {
            // Handle input for testing different blends
            if (Input.GetKeyDown(_idleKey))
            {
                PlayIdleBlend();
            }
            else if (Input.GetKeyDown(_walkKey))
            {
                PlayWalkBlend();
            }
            else if (Input.GetKeyDown(_runKey))
            {
                PlayRunBlend();
            }
        }

        /// <summary>
        /// Play the idle blend using the extension method
        /// </summary>
        public void PlayIdleBlend()
        {
            if (_idleBlend == null)
            {
                Debug.LogWarning("Idle blend asset is not assigned!", this);
                return;
            }

            // This is the main integration - using Animancer.Play(blendAsset, fadeTime)
            var state = _animancer.Play(_idleBlend, _defaultFadeTime);
            
            Debug.Log($"Playing idle blend: {_idleBlend.name} with fade time: {_defaultFadeTime}");
            
            // You can access the MagicBlendAnimancerState if needed
            var magicBlendState = state.AsMagicBlendState();
            if (magicBlendState != null)
            {
                Debug.Log($"MagicBlend state created successfully. Asset: {magicBlendState.Asset.name}");
            }
        }

        /// <summary>
        /// Play the walk blend using the extension method
        /// </summary>
        public void PlayWalkBlend()
        {
            if (_walkBlend == null)
            {
                Debug.LogWarning("Walk blend asset is not assigned!", this);
                return;
            }

            // Using the extension method with custom fade mode
            var state = _animancer.Play(_walkBlend, _defaultFadeTime, FadeMode.FixedSpeed);
            
            Debug.Log($"Playing walk blend: {_walkBlend.name}");
        }

        /// <summary>
        /// Play the run blend using the extension method
        /// </summary>
        public void PlayRunBlend()
        {
            if (_runBlend == null)
            {
                Debug.LogWarning("Run blend asset is not assigned!", this);
                return;
            }

            // Alternative approach: create transition first, then play
            var transition = _runBlend.CreateTransition(_defaultFadeTime);
            var state = _animancer.Play(transition);
            
            Debug.Log($"Playing run blend: {_runBlend.name}");
            
            // Example of modifying the state after playing
            if (state != null)
            {
                state.Speed = 1.2f; // Make it slightly faster
            }
        }

        /// <summary>
        /// Example of playing on a specific layer
        /// </summary>
        public void PlayOnLayer(int layerIndex, MagicBlendAsset blendAsset)
        {
            if (blendAsset == null)
            {
                Debug.LogWarning("Blend asset is null!", this);
                return;
            }

            if (layerIndex < 0 || layerIndex >= _animancer.Graph.Layers.Count)
            {
                Debug.LogWarning($"Invalid layer index: {layerIndex}", this);
                return;
            }

            // Play on a specific layer
            var state = _animancer.Graph.Layers[layerIndex].Play(blendAsset, _defaultFadeTime);
            Debug.Log($"Playing {blendAsset.name} on layer {layerIndex}");
        }

        /// <summary>
        /// Example of runtime modification of MagicBlendAsset properties
        /// </summary>
        public void ModifyBlendAtRuntime(MagicBlendAsset blendAsset, float newGlobalWeight)
        {
            if (blendAsset == null) return;

            // Modify the asset properties at runtime
            blendAsset.globalWeight = Mathf.Clamp01(newGlobalWeight);
            
            // If the asset is currently playing, you might need to update the state
            var currentState = _animancer.States.Current;
            if (currentState != null && currentState.IsMagicBlendState())
            {
                var magicBlendState = currentState.AsMagicBlendState();
                if (magicBlendState != null && magicBlendState.Asset == blendAsset)
                {
                    Debug.Log($"Modified global weight of currently playing blend to: {newGlobalWeight}");
                    // The changes will be applied on the next frame
                }
            }
        }

        /// <summary>
        /// Example of checking what's currently playing
        /// </summary>
        public void CheckCurrentState()
        {
            var currentState = _animancer.States.Current;
            if (currentState == null)
            {
                Debug.Log("No animation currently playing");
                return;
            }

            if (currentState.IsMagicBlendState())
            {
                var magicBlendState = currentState.AsMagicBlendState();
                Debug.Log($"Currently playing MagicBlend: {magicBlendState.Asset.name}");
                Debug.Log($"  - Length: {magicBlendState.Length}");
                Debug.Log($"  - Time: {magicBlendState.Time:F2}");
                Debug.Log($"  - Weight: {magicBlendState.Weight:F2}");
                Debug.Log($"  - Speed: {magicBlendState.Speed:F2}");
            }
            else
            {
                Debug.Log($"Currently playing regular animation: {currentState.MainObject?.name}");
            }
        }

        private void OnValidate()
        {
            // Auto-assign Animancer component if not set
            if (_animancer == null)
                _animancer = GetComponent<AnimancerComponent>();
        }
    }
}
