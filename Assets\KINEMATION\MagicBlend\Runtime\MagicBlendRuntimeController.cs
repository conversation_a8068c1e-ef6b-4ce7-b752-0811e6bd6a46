// Example script showing how to modify MagicBlendAsset properties at runtime
// -----------------------------------------------------------------------------

using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using NewAnimancer;
using UnityEngine;
using YourProject.Animation;

/// <summary>
/// Example controller for modifying MagicBlendAsset properties at runtime
/// </summary>
public class MagicBlendRuntimeController : MonoBehaviour
{
    [Header("Components")]
    [SerializeField] private HybridAnimancerComponent animancer;
    
    [Header("MagicBlend Assets")]
    [SerializeField] private MagicBlendAsset targetAsset;
    [SerializeField] private MagicBlendTransition targetTransition;
    
    [Header("Runtime Pose Options")]
    [SerializeField] private AnimationClip[] basePoseOptions;
    [SerializeField] private AnimationClip[] overlayPoseOptions;
    
    [Header("Runtime Settings")]
    [SerializeField] private bool useRuntimeCopy = true;
    [SerializeField] private float newBlendTime = 0.25f;
    [SerializeField, Range(0f, 1f)] private float newGlobalWeight = 1f;

    private MagicBlendAsset runtimeAsset;
    private int currentBasePoseIndex = 0;
    private int currentOverlayPoseIndex = 0;

    private void Start()
    {
        if (animancer == null)
            animancer = GetComponent<HybridAnimancerComponent>();
            
        // Create a runtime copy if requested
        if (useRuntimeCopy && targetAsset != null)
        {
            runtimeAsset = targetAsset.CreateRuntimeCopy();
            Debug.Log("[MagicBlendRuntimeController] Created runtime copy for safe modification");
        }
        else
        {
            runtimeAsset = targetAsset;
        }
    }

    private void Update()
    {
        HandleInput();
    }

    private void HandleInput()
    {
        // Change base pose
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            ChangeBasePose();
        }
        
        // Change overlay pose
        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            ChangeOverlayPose();
        }
        
        // Change blend time
        if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            ChangeBlendTime();
        }
        
        // Change global weight
        if (Input.GetKeyDown(KeyCode.Alpha4))
        {
            ChangeGlobalWeight();
        }
        
        // Play with current settings
        if (Input.GetKeyDown(KeyCode.Space))
        {
            PlayWithCurrentSettings();
        }
    }

    #region Public Methods

    /// <summary>
    /// Changes the base pose to the next available option
    /// </summary>
    public void ChangeBasePose()
    {
        if (runtimeAsset == null || basePoseOptions == null || basePoseOptions.Length == 0)
        {
            Debug.LogWarning("[MagicBlendRuntimeController] No base pose options available");
            return;
        }

        currentBasePoseIndex = (currentBasePoseIndex + 1) % basePoseOptions.Length;
        var newPose = basePoseOptions[currentBasePoseIndex];

        // Use the new method that forces replay to ensure base pose is applied
        runtimeAsset.SetBasePoseAndReplay(newPose, animancer, 0.1f);
        Debug.Log($"[MagicBlendRuntimeController] Changed to base pose: {newPose.name}");
    }

    /// <summary>
    /// Changes the overlay pose to the next available option
    /// </summary>
    public void ChangeOverlayPose()
    {
        if (runtimeAsset == null || overlayPoseOptions == null || overlayPoseOptions.Length == 0)
        {
            Debug.LogWarning("[MagicBlendRuntimeController] No overlay pose options available");
            return;
        }

        currentOverlayPoseIndex = (currentOverlayPoseIndex + 1) % overlayPoseOptions.Length;
        var newPose = overlayPoseOptions[currentOverlayPoseIndex];
        
        runtimeAsset.SetOverlayPose(newPose, true);
        Debug.Log($"[MagicBlendRuntimeController] Changed to overlay pose: {newPose.name}");
    }

    /// <summary>
    /// Sets a specific base pose by index
    /// </summary>
    /// <param name="index">Index of the pose in basePoseOptions array</param>
    public void SetBasePose(int index)
    {
        if (runtimeAsset == null || basePoseOptions == null || index < 0 || index >= basePoseOptions.Length)
        {
            Debug.LogWarning($"[MagicBlendRuntimeController] Invalid base pose index: {index}");
            return;
        }

        currentBasePoseIndex = index;
        var newPose = basePoseOptions[index];
        runtimeAsset.SetBasePoseAndReplay(newPose, animancer, 0.1f);
        Debug.Log($"[MagicBlendRuntimeController] Set base pose to: {newPose.name}");
    }

    /// <summary>
    /// Sets a specific overlay pose by index
    /// </summary>
    /// <param name="index">Index of the pose in overlayPoseOptions array</param>
    public void SetOverlayPose(int index)
    {
        if (runtimeAsset == null || overlayPoseOptions == null || index < 0 || index >= overlayPoseOptions.Length)
        {
            Debug.LogWarning($"[MagicBlendRuntimeController] Invalid overlay pose index: {index}");
            return;
        }

        currentOverlayPoseIndex = index;
        var newPose = overlayPoseOptions[index];
        runtimeAsset.SetOverlayPose(newPose, true);
        Debug.Log($"[MagicBlendRuntimeController] Set overlay pose to: {newPose.name}");
    }

    /// <summary>
    /// Sets both poses at once
    /// </summary>
    /// <param name="basePose">The new base pose</param>
    /// <param name="overlayPose">The new overlay pose</param>
    public void SetBothPoses(AnimationClip basePose, AnimationClip overlayPose)
    {
        if (runtimeAsset == null)
        {
            Debug.LogWarning("[MagicBlendRuntimeController] No runtime asset available");
            return;
        }

        runtimeAsset.SetPoses(basePose, overlayPose, true);
        Debug.Log($"[MagicBlendRuntimeController] Set both poses - Base: {basePose?.name}, Overlay: {overlayPose?.name}");
    }

    /// <summary>
    /// Changes the blend time
    /// </summary>
    public void ChangeBlendTime()
    {
        if (runtimeAsset == null) return;

        runtimeAsset.SetBlendTime(newBlendTime, true);
        Debug.Log($"[MagicBlendRuntimeController] Changed blend time to: {newBlendTime}s");
    }

    /// <summary>
    /// Changes the global weight
    /// </summary>
    public void ChangeGlobalWeight()
    {
        if (runtimeAsset == null) return;

        runtimeAsset.SetGlobalWeight(newGlobalWeight, true);
        Debug.Log($"[MagicBlendRuntimeController] Changed global weight to: {newGlobalWeight}");
    }

    /// <summary>
    /// Plays the animation with current settings
    /// </summary>
    public void PlayWithCurrentSettings()
    {
        if (animancer == null || runtimeAsset == null)
        {
            Debug.LogWarning("[MagicBlendRuntimeController] Missing components for playback");
            return;
        }

        // Play using the runtime asset
        var state = animancer.Play(runtimeAsset, runtimeAsset.blendTime);
        Debug.Log($"[MagicBlendRuntimeController] Playing with current settings: {state}");
    }

    #endregion

    #region GUI for Testing

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 250));
        GUILayout.Label("MagicBlend Runtime Controller", GUI.skin.box);
        
        if (GUILayout.Button("1 - Change Base Pose"))
            ChangeBasePose();
            
        if (GUILayout.Button("2 - Change Overlay Pose"))
            ChangeOverlayPose();
            
        if (GUILayout.Button("3 - Change Blend Time"))
            ChangeBlendTime();
            
        if (GUILayout.Button("4 - Change Global Weight"))
            ChangeGlobalWeight();
            
        GUILayout.Space(10);
        
        if (GUILayout.Button("SPACE - Play Animation"))
            PlayWithCurrentSettings();
            
        GUILayout.Space(10);
        
        if (runtimeAsset != null)
        {
            GUILayout.Label("Current Settings:", GUI.skin.label);
            GUILayout.Label($"Base: {(runtimeAsset.basePose ? runtimeAsset.basePose.name : "None")}");
            GUILayout.Label($"Overlay: {(runtimeAsset.overlayPose ? runtimeAsset.overlayPose.name : "None")}");
            GUILayout.Label($"Blend Time: {runtimeAsset.blendTime:F2}s");
            GUILayout.Label($"Weight: {runtimeAsset.globalWeight:F2}");
        }
        
        GUILayout.EndArea();
    }

    #endregion
}
