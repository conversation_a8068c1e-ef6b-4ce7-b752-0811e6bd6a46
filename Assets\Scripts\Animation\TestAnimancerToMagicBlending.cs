﻿using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using Module.Mono.Animancer.RealsticFemale;
using NewAnimancer;
using UnityEngine;
using YourProject.Animation;

/// <summary>
/// Test script demonstrating the new MagicBlend + Animancer integration.
/// Shows both the old approach and the new simplified approach.
/// </summary>
public class TestAnimancerToMagicBlending : MonoBehaviour
{
    [Header("Components")]
    [SerializeField] private HybridAnimancerComponent hybridAnimancerComponent;
    [SerializeField] private DynamicLayeredCharacterAnimations DynamicLayeredCharacterAnimations;

    [Header("Animations")]
    [SerializeField] private ClipTransition _MainAnimation;
    [SerializeField] private MagicBlendAsset blendAsset;

    [Header("New Integration Test")]
    [SerializeField] private MagicBlendTransition magicBlendTransition;
    [SerializeField] private bool useNewIntegration = true;
    [SerializeField] private float fadeTime = 0.25f;

    void Start()
    {
        // Play main animation
        hybridAnimancerComponent.Play(_MainAnimation, 0.25f);

        if (useNewIntegration)
        {
            // NEW APPROACH: Direct MagicBlendAsset playback through Animancer
            TestNewIntegration();
        }
        else
        {
            // OLD APPROACH: Through LayeredAnimationManager
            DynamicLayeredCharacterAnimations.PlayUpperBodyAnimation(blendAsset);
        }
    }

    private void TestNewIntegration()
    {
        Debug.Log("[TestAnimancerToMagicBlending] Testing new MagicBlend + Animancer integration");

        if (blendAsset != null)
        {
            // Method 1: Direct asset playback using extension methods
            Debug.Log("Method 1: Direct asset playback");
            var state1 = hybridAnimancerComponent.Play(blendAsset, fadeTime);
            Debug.Log($"Created state: {state1}");
        }

        if (magicBlendTransition != null)
        {
            // Method 2: Using MagicBlendTransition asset
            Debug.Log("Method 2: Using transition asset");
            var state2 = hybridAnimancerComponent.Play(magicBlendTransition, fadeTime);
            Debug.Log($"Created state: {state2}");
        }
    }

    private void Update()
    {
        // Test controls
        if (Input.GetKeyDown(KeyCode.Alpha1) && blendAsset != null)
        {
            Debug.Log("Playing MagicBlendAsset directly");
            hybridAnimancerComponent.Play(blendAsset, fadeTime);
        }

        if (Input.GetKeyDown(KeyCode.Alpha2) && magicBlendTransition != null)
        {
            Debug.Log("Playing MagicBlendTransition");
            hybridAnimancerComponent.Play(magicBlendTransition, fadeTime);
        }

        if (Input.GetKeyDown(KeyCode.Alpha3) && blendAsset != null)
        {
            Debug.Log("Stopping MagicBlendAsset");
            hybridAnimancerComponent.Stop(blendAsset);
        }
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("MagicBlend Integration Test", GUI.skin.box);

        if (GUILayout.Button("1 - Play MagicBlendAsset"))
        {
            if (blendAsset != null)
                hybridAnimancerComponent.Play(blendAsset, fadeTime);
        }

        if (GUILayout.Button("2 - Play MagicBlendTransition"))
        {
            if (magicBlendTransition != null)
                hybridAnimancerComponent.Play(magicBlendTransition, fadeTime);
        }

        if (GUILayout.Button("3 - Stop MagicBlendAsset"))
        {
            if (blendAsset != null)
                hybridAnimancerComponent.Stop(blendAsset);
        }

        GUILayout.Space(10);
        GUILayout.Label($"Current State: {hybridAnimancerComponent.States.Current?.ToString() ?? "None"}");

        GUILayout.EndArea();
    }
}
